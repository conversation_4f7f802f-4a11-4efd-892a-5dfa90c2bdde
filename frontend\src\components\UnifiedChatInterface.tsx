import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Typography,
  Button,
  CircularProgress,
  Divider,
  Chip,
  useTheme,
  Tabs,
  Tab,
  Badge,
  TextField,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  Chat as ChatIcon,
  History as HistoryIcon,
  AutoAwesome as MultiAgentIcon,
  SmartToy as AIIcon,
} from '@mui/icons-material';
import { FiSend } from 'react-icons/fi';
import { styled, alpha } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../app/store';
import { useAppDispatch, useAppSelector } from '../app/hooks';
import { getRoleConfigs, getCommonMessages, getErrorMessages, type RoleConfig } from './UnifiedChatInterface/roleConfigs';
import {
  fetchConversations,
  deleteConversation,
  setActiveConversation,
  createConversation,
} from '../features/chat/chatSlice';
import Chat from '../features/chat/Chat';
import multiAgentService from '../services/multiAgentService';

// Styled Components
const PageContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: 'calc(100vh - 64px)',
  backgroundColor: theme.palette.background.default,
  overflow: 'hidden',
}));

const SidebarPaper = styled(Paper)(({ theme }) => ({
  width: 320,
  minWidth: 320,
  height: '100%',
  borderRadius: theme.spacing(2),
  borderInlineEnd: `1px solid ${theme.palette.divider}`,
  background: theme.palette.mode === 'dark' 
    ? `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.default, 0.95)} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha('#f5f7fa', 0.9)} 100%)`,
  backdropFilter: 'blur(20px)',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  boxShadow: theme.palette.mode === 'dark' 
    ? `0 8px 32px ${alpha('#000', 0.3)}`
    : `0 8px 32px ${alpha('#000', 0.1)}`,
}));

const ChatPaper = styled(Paper)(({ theme }) => ({
  flex: 1,
  height: '100%',
  borderRadius: theme.spacing(2),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.default, 0.95)} 100%)`
    : `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha('#f8fafc', 0.95)} 100%)`,
  backdropFilter: 'blur(20px)',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  boxShadow: theme.palette.mode === 'dark' 
    ? `0 8px 32px ${alpha('#000', 0.3)}`
    : `0 8px 32px ${alpha('#000', 0.1)}`,
  marginInlineStart: theme.spacing(2),
}));

const HeaderBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: theme.palette.primary.contrastText,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    height: 3,
    borderRadius: '3px 3px 0 0',
  },
  '& .MuiTab-root': {
    minHeight: 60,
    textTransform: 'none',
    fontSize: '1rem',
    fontWeight: 500,
    color: theme.palette.text.secondary,
    '&.Mui-selected': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
    '&:hover': {
      color: theme.palette.primary.main,
      background: alpha(theme.palette.primary.main, 0.05),
    },
  },
}));

const StyledListItem = styled(ListItem)(({ theme }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '3px',
    height: '100%',
    background: 'transparent',
    transition: 'all 0.3s ease',
    borderRadius: '0 3px 3px 0',
  },
  '&:hover': {
    background: alpha(theme.palette.primary.main, 0.08),
    transform: 'translateX(4px)',
    '&::before': {
      background: alpha(theme.palette.primary.main, 0.3),
    },
  },
  '&.Mui-selected': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.15)} 0%, ${alpha(theme.palette.secondary.main, 0.15)} 100%)`
      : `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.12)} 0%, ${alpha(theme.palette.secondary.main, 0.12)} 100%)`,
    '&::before': {
      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    },
  },
}));

const CreateButton = styled(Button)(({ theme }) => ({
  margin: theme.spacing(1),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
  color: theme.palette.primary.contrastText,
  borderRadius: theme.spacing(2),
  textTransform: 'none',
  fontWeight: 600,
  padding: theme.spacing(1.5, 3),
  boxShadow: theme.palette.mode === 'dark'
    ? `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`
    : `0 4px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
  '&:hover': {
    background: theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`
      : `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.dark} 100%)`,
    transform: 'translateY(-2px)',
    boxShadow: theme.palette.mode === 'dark'
      ? `0 8px 25px ${alpha(theme.palette.primary.main, 0.4)}`
      : `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
  },
}));

// Interfaces for Multi-Agent Chat
interface RoleConfig {
  name: string;
  code: string;
  color: string;
  icon: string;
  agents: AgentInfo[];
  sampleMessages: string[];
  description: string;
}

interface AgentInfo {
  name: string;
  icon: string;
  color: string;
  description: string;
  keywords: string[];
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  agentUsed?: string;
  timestamp: Date;
  metadata?: any;
}

// Role configurations are now loaded dynamically with translations
// (Moved to roleConfigs.ts and loaded via getRoleConfigs function)




interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`chat-tabpanel-${index}`}
      aria-labelledby={`chat-tab-${index}`}
      style={{ height: '100%', display: value === index ? 'flex' : 'none', flexDirection: 'column' }}
      {...other}
    >
      {value === index && children}
    </div>
  );
}

const UnifiedChatInterface: React.FC = () => {
  const dispatch = useAppDispatch();
  const theme = useTheme();
  const { t } = useTranslation(['translation', 'aiAgents']);
  const { user } = useSelector((state: RootState) => state.auth);

  // Get translated role configurations
  const roleConfigs = useMemo(() => getRoleConfigs(t), [t]);
  const commonMessages = useMemo(() => getCommonMessages(t), [t]);
  const errorMessages = useMemo(() => getErrorMessages(t), [t]);
  
  // Standard chat state
  const {
    conversations,
    activeConversation,
    loading,
  } = useAppSelector((state) => state.chat);
  const [selectedConversationId, setSelectedConversationId] = useState<number | null>(null);
  
  // Multi-agent chat state
  const [multiAgentMessages, setMultiAgentMessages] = useState<ChatMessage[]>([]);
  const [multiAgentInput, setMultiAgentInput] = useState('');
  const [multiAgentLoading, setMultiAgentLoading] = useState(false);
  const [activeAgent, setActiveAgent] = useState<string | null>(null);
  const messageEndRef = useRef<HTMLDivElement>(null);
  
  // Tab state
  const [tabValue, setTabValue] = useState(0);
  
  // Get user's role configuration
  const getUserRoleConfig = (): RoleConfig => {
    const userRole = user?.role?.toUpperCase() || 'STUDENT';
    return roleConfigs.find(role => role.code === userRole) || roleConfigs[0];
  };
  
  const currentRole = getUserRoleConfig();
  const conversationsArray = Array.isArray(conversations) ? conversations : [];

  useEffect(() => {
    dispatch(fetchConversations());
  }, [dispatch]);

  useEffect(() => {
    if (activeConversation) {
      setSelectedConversationId(activeConversation.id);
    } else if (conversationsArray.length > 0 && !selectedConversationId) {
      setSelectedConversationId(conversationsArray[0].id);
      dispatch(setActiveConversation(conversationsArray[0]));
    }
  }, [activeConversation, conversationsArray, selectedConversationId, dispatch]);

  const scrollToBottom = useCallback(() => {
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [multiAgentMessages, scrollToBottom]);

  // Standard chat handlers
  const handleSelectConversation = (conversationId: number) => {
    setSelectedConversationId(conversationId);
    const conversation = conversationsArray.find(c => c.id === conversationId);
    if (conversation) {
      dispatch(setActiveConversation(conversation));
    }
  };

  const handleDeleteConversation = async (conversationId: number) => {
    await dispatch(deleteConversation(conversationId));
    if (selectedConversationId === conversationId) {
      const remainingConversations = conversationsArray.filter(c => c.id !== conversationId);
      if (remainingConversations.length > 0) {
        handleSelectConversation(remainingConversations[0].id);
      } else {
        setSelectedConversationId(null);
        dispatch(setActiveConversation(null));
      }
    }
  };

  const handleCreateConversation = async () => {
    try {
      const result = await dispatch(createConversation({
        title: t('chat.defaultConversationTitle', 'ChatGPT'),
      }));
      
      if (createConversation.fulfilled.match(result)) {
        if (result.payload.id) {
          setSelectedConversationId(result.payload.id);
        }
      }
    } catch (error) {
      console.error('Failed to create conversation:', error);
    }
  };

  // Multi-agent chat handlers
  const predictAgent = (message: string, role: RoleConfig): AgentInfo => {
    const lowerMessage = message.toLowerCase();
    for (const agent of role.agents) {
      if (agent.keywords.some(keyword => lowerMessage.includes(keyword))) {
        return agent;
      }
    }
    return role.agents[0];
  };

  const sendMultiAgentMessage = async () => {
    if (!multiAgentInput.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: multiAgentInput,
      isUser: true,
      timestamp: new Date(),
    };

    setMultiAgentMessages(prev => [...prev, userMessage]);
    setMultiAgentLoading(true);

    const predictedAgent = predictAgent(multiAgentInput, currentRole);
    setActiveAgent(predictedAgent.name);

    try {
      const response = await multiAgentService.testAgentResponse({
        agent_type: predictedAgent.name.toLowerCase().replace(' ', '_'),
        message: multiAgentInput,
        context: {
          user_role: user?.role,
          user_name: user?.first_name || user?.username,
          user_id: user?.id,
        },
      });

      let content = '';
      if (response?.data?.response?.content) {
        content = response.data.response.content;
      } else if (response?.data?.message) {
        content = response.data.message;
      } else if (response?.message) {
        content = response.message;
      } else {
        content = `${currentRole.icon} ${predictedAgent.name}: I received your message, ${user?.first_name || user?.username || 'there'}! I'm here to help with ${predictedAgent.description.toLowerCase()}.`;
      }

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content,
        isUser: false,
        agentUsed: predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
        metadata: response?.data?.response?.metadata || response?.metadata,
      };

      setMultiAgentMessages(prev => [...prev, botMessage]);
    } catch (error: any) {
      console.error('Multi-agent chat error:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `${currentRole.icon} Hello ${user?.first_name || user?.username || 'there'}! As a ${currentRole.name}, this would be handled by ${predictedAgent.name} (${predictedAgent.icon}). The AI system recognizes your role and will provide ${currentRole.name.toLowerCase()}-specific assistance.`,
        isUser: false,
        agentUsed: predictedAgent.name.toLowerCase().replace(' ', '_'),
        timestamp: new Date(),
      };
      setMultiAgentMessages(prev => [...prev, errorMessage]);
    }

    setMultiAgentInput('');
    setMultiAgentLoading(false);
    setActiveAgent(null);
  };

  const getAgentInfo = (agentUsed: string): AgentInfo => {
    for (const agent of currentRole.agents) {
      if (agentUsed?.includes(agent.name.toLowerCase().replace(' ', '_'))) {
        return agent;
      }
    }
    return currentRole.agents[0];
  };

  const selectedConversation = conversationsArray.find(c => c.id === selectedConversationId);

  return (
    <PageContainer>
      {/* Sidebar */}
      <SidebarPaper elevation={0}>
        <HeaderBox>
          <Box display="flex" alignItems="center" gap={1}>
            <AIIcon />
            <Typography variant="h6" fontWeight={600}>
              AI Chat Assistant
            </Typography>
          </Box>
          <Chip 
            label={conversationsArray.length} 
            size="small" 
            sx={{ 
              backgroundColor: alpha(theme.palette.background.paper, 0.2), 
              color: theme.palette.primary.contrastText,
              fontWeight: 600,
              border: `1px solid ${alpha(theme.palette.primary.contrastText, 0.2)}`,
            }} 
          />
        </HeaderBox>

        {/* Only show conversation list when on Standard Chat tab */}
        {tabValue === 0 && (
          <>
            <Box sx={{ p: 2, display: 'flex', gap: 1 }}>
              <CreateButton
                fullWidth
                startIcon={<AddIcon />}
                onClick={handleCreateConversation}
              >
                {t('chat.createNewConversation', 'New Conversation')}
              </CreateButton>
            </Box>

            <Divider sx={{ opacity: 0.1 }} />

            <Box sx={{ flex: 1, overflow: 'auto' }}>
              {loading ? (
                <Box display="flex" justifyContent="center" p={3}>
                  <CircularProgress size={24} />
                </Box>
              ) : conversationsArray.length === 0 ? (
                <Box p={3} textAlign="center">
                  <HistoryIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body2" color="text.secondary">
                    {t('chat.noConversations', 'No conversations yet')}
                  </Typography>
                </Box>
              ) : (
                <List sx={{ py: 1 }}>
                  {conversationsArray.map((conversation) => (
                    <StyledListItem
                      key={conversation.id}
                      selected={selectedConversationId === conversation.id}
                      onClick={() => handleSelectConversation(conversation.id)}
                    >
                      <ListItemText
                        primary={
                          <Typography 
                            variant="subtitle2" 
                            fontWeight={selectedConversationId === conversation.id ? 600 : 500}
                            sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              color: selectedConversationId === conversation.id 
                                ? theme.palette.primary.main 
                                : theme.palette.text.primary,
                            }}
                          >
                            {conversation.title || 'Untitled Conversation'}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {new Date(conversation.created_at).toLocaleDateString()}
                          </Typography>
                        }
                      />
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteConversation(conversation.id);
                        }}
                        sx={{
                          opacity: 0.6,
                          '&:hover': {
                            opacity: 1,
                            color: 'error.main',
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </StyledListItem>
                  ))}
                </List>
              )}
            </Box>
          </>
        )}

        {/* Show agents when on Multi-Agent Chat tab */}
        {tabValue === 1 && (
          <>
            <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
              <Typography variant='h6' gutterBottom fontWeight={600}>
                🎯 Available Agents
              </Typography>
              {currentRole.agents.map((agent, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Chip
                    icon={<span>{agent.icon}</span>}
                    label={agent.name}
                    variant={activeAgent === agent.name ? 'filled' : 'outlined'}
                    color={activeAgent === agent.name ? 'primary' : 'default'}
                    sx={{ 
                      mb: 1, 
                      width: '100%', 
                      justifyContent: 'flex-start',
                      borderRadius: 2,
                      px: 1,
                    }}
                  />
                  <Typography
                    variant='caption'
                    display='block'
                    color='text.secondary'
                    sx={{ px: 1, mb: 0.5 }}
                  >
                    {agent.description}
                  </Typography>
                  <Typography
                    variant='caption'
                    display='block'
                    sx={{ px: 1, color: 'primary.main', fontSize: '0.7rem' }}
                  >
                    Keywords: {agent.keywords.join(', ')}
                  </Typography>
                </Box>
              ))}
            </Box>

            <Box sx={{ p: 3, flex: 1, overflow: 'auto' }}>
              <Typography variant='subtitle2' gutterBottom fontWeight={600}>
                ✨ Try these {currentRole.name.toLowerCase()} messages:
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {currentRole.sampleMessages.map((sample, index) => (
                  <Chip
                    key={index}
                    label={sample}
                    onClick={() => setMultiAgentInput(sample)}
                    variant='outlined'
                    size='small'
                    clickable
                    sx={{
                      justifyContent: 'flex-start',
                      borderRadius: 2,
                      px: 1.5,
                      py: 0.5,
                      height: 'auto',
                      '& .MuiChip-label': {
                        whiteSpace: 'normal',
                        textAlign: 'left',
                        padding: '8px 0',
                      },
                      backgroundColor: alpha(theme.palette.primary.main, 0.08),
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                      color: 'primary.main',
                    }}
                  />
                ))}
              </Box>
            </Box>
          </>
        )}
      </SidebarPaper>

      {/* Chat Area */}
      <ChatPaper elevation={0}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <StyledTabs
            value={tabValue}
            onChange={(_, newValue) => setTabValue(newValue)}
            aria-label="chat tabs"
          >
            <Tab
              icon={<ChatIcon />}
              label="Standard Chat"
              iconPosition="start"
              sx={{ minHeight: 60 }}
            />
            <Tab
              icon={
                <Badge badgeContent={currentRole.agents.length} color="primary">
                  <MultiAgentIcon />
                </Badge>
              }
              label={`Multi-Agent (${currentRole.name})`}
              iconPosition="start"
              sx={{ minHeight: 60 }}
            />
          </StyledTabs>
        </Box>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          {/* Standard Chat */}
          {selectedConversation ? (
            <Chat conversationId={selectedConversation.id} />
          ) : (
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center',
              flex: 1,
              p: 4,
              textAlign: 'center'
            }}>
              <ChatIcon sx={{ fontSize: 80, color: 'primary.main', mb: 3, opacity: 0.7 }} />
              <Typography variant="h5" gutterBottom>
                Welcome to Standard Chat
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Create a new conversation or select an existing one to start chatting with AI.
              </Typography>
              <CreateButton
                startIcon={<AddIcon />}
                onClick={handleCreateConversation}
              >
                Start New Conversation
              </CreateButton>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* Multi-Agent Chat */}
          <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            {/* Multi-Agent Header */}
            <Box sx={{ 
              p: 2, 
              borderBottom: '1px solid', 
              borderColor: 'divider',
              background: alpha(currentRole.color, 0.05)
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    background: `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.7)} 100%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 600,
                  }}
                >
                  <span style={{ fontSize: '20px' }}>{currentRole.icon}</span>
                </Box>
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    Multi-Agent Chat - {currentRole.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Welcome {user?.first_name || user?.username || 'User'}! {currentRole.description}
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Messages */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
              {multiAgentMessages.length === 0 ? (
                <Box sx={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  height: '100%',
                  textAlign: 'center'
                }}>
                  <MultiAgentIcon sx={{ fontSize: 80, color: 'primary.main', mb: 3, opacity: 0.7 }} />
                  <Typography variant="h5" gutterBottom>
                    Welcome to Multi-Agent Chat
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    {currentRole.description}. Choose from the sample messages or start typing!
                  </Typography>
                </Box>
              ) : (
                <>
                  {multiAgentMessages.map(message => (
                    <Box
                      key={message.id}
                      sx={{
                        display: 'flex',
                        justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                        mb: 2,
                      }}
                    >
                      <Paper
                        sx={{
                          p: 2,
                          maxWidth: '70%',
                          borderRadius: message.isUser ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                          background: message.isUser 
                            ? `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.8)} 100%)`
                            : theme.palette.background.paper,
                          color: message.isUser ? 'white' : 'text.primary',
                        }}
                      >
                        {!message.isUser && message.agentUsed && (
                          <Box sx={{ mb: 1 }}>
                            <Chip
                              icon={<span>{getAgentInfo(message.agentUsed).icon}</span>}
                              label={getAgentInfo(message.agentUsed).name}
                              size='small'
                              sx={{
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                color: 'primary.main',
                              }}
                            />
                          </Box>
                        )}
                        <Typography variant='body2'>
                          {message.content}
                        </Typography>
                        <Typography variant='caption' sx={{ opacity: 0.7, display: 'block', mt: 1 }}>
                          {message.timestamp.toLocaleTimeString()}
                        </Typography>
                      </Paper>
                    </Box>
                  ))}
                  {multiAgentLoading && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                      <Paper sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CircularProgress size={16} />
                        <Typography variant='body2'>
                          {activeAgent} is thinking...
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                  <div ref={messageEndRef} />
                </>
              )}
            </Box>

            {/* Multi-Agent Input */}
            <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
              <Paper
                component='form'
                onSubmit={(e) => {
                  e.preventDefault();
                  sendMultiAgentMessage();
                }}
                sx={{
                  p: 2,
                  display: 'flex',
                  alignItems: 'flex-end',
                  gap: 2,
                  borderRadius: 3,
                }}
              >
                <TextField
                  fullWidth
                  value={multiAgentInput}
                  onChange={e => setMultiAgentInput(e.target.value)}
                  placeholder={`Type a message as ${currentRole.name.toLowerCase()}...`}
                  disabled={multiAgentLoading}
                  multiline
                  maxRows={4}
                  variant="standard"
                  InputProps={{
                    disableUnderline: true,
                  }}
                />
                <IconButton
                  type='submit'
                  disabled={!multiAgentInput.trim() || multiAgentLoading}
                  sx={{
                    background: `linear-gradient(135deg, ${currentRole.color} 0%, ${alpha(currentRole.color, 0.8)} 100%)`,
                    color: 'white',
                    '&:hover': {
                      background: `linear-gradient(135deg, ${alpha(currentRole.color, 0.9)} 0%, ${alpha(currentRole.color, 0.7)} 100%)`,
                    },
                  }}
                >
                  {multiAgentLoading ? (
                    <CircularProgress size={20} sx={{ color: 'white' }} />
                  ) : (
                    <FiSend size={20} />
                  )}
                </IconButton>
              </Paper>

              {multiAgentInput && (
                <Box sx={{ mt: 1, textAlign: 'center' }}>
                  <Typography variant='caption' color='text.secondary'>
                    Predicted agent: {predictAgent(multiAgentInput, currentRole).icon}{' '}
                    {predictAgent(multiAgentInput, currentRole).name}
                  </Typography>
                </Box>
              )}
            </Box>
          </Box>
        </TabPanel>
      </ChatPaper>
    </PageContainer>
  );
};

export default UnifiedChatInterface;
