/**
 * Role configurations for the Unified Chat Interface
 * Uses translation keys instead of hardcoded strings
 */

export interface Agent {
  name: string;
  icon: string;
  color: string;
  description: string;
  keywords: string[];
}

export interface RoleConfig {
  name: string;
  code: string;
  color: string;
  icon: string;
  description: string;
  agents: Agent[];
  sampleMessages: string[];
}

/**
 * Get role configurations with translations
 */
export const getRoleConfigs = (t: any): RoleConfig[] => [
  {
    name: t('aiAgents.student.title', { ns: 'aiAgents' }),
    code: 'STUDENT',
    color: '#2196F3',
    icon: '🎓',
    description: t('aiAgents.student.description', { ns: 'aiAgents' }),
    agents: [
      {
        name: t('aiAgents.student.agents.mathTutor.name', { ns: 'aiAgents' }),
        icon: '🔢',
        color: '#4CAF50',
        description: t('aiAgents.student.agents.mathTutor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.student.agents.mathTutor.keywords', { ns: 'aiAgents' }) || ['math', 'equation', 'calculate'],
      },
      {
        name: t('aiAgents.student.agents.scienceTutor.name', { ns: 'aiAgents' }),
        icon: '🔬',
        color: '#FF9800',
        description: t('aiAgents.student.agents.scienceTutor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.student.agents.scienceTutor.keywords', { ns: 'aiAgents' }) || ['science', 'biology', 'chemistry'],
      },
      {
        name: t('aiAgents.student.agents.languageTutor.name', { ns: 'aiAgents' }),
        icon: '📝',
        color: '#9C27B0',
        description: t('aiAgents.student.agents.languageTutor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.student.agents.languageTutor.keywords', { ns: 'aiAgents' }) || ['write', 'essay', 'grammar'],
      },
      {
        name: t('aiAgents.student.agents.careerAdvisor.name', { ns: 'aiAgents' }),
        icon: '🎯',
        color: '#F44336',
        description: t('aiAgents.student.agents.careerAdvisor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.student.agents.careerAdvisor.keywords', { ns: 'aiAgents' }) || ['career', 'advice', 'guidance'],
      },
    ],
    sampleMessages: t('aiAgents.student.sampleMessages', { ns: 'aiAgents' }) || [
      'Help me understand quadratic equations',
      'Explain photosynthesis process',
      'How do I write a better essay?',
      'What career path should I choose?',
      'I need help with my homework',
    ],
  },
  {
    name: t('aiAgents.professor.title', { ns: 'aiAgents' }),
    code: 'PROFESSOR',
    color: '#4CAF50',
    icon: '👨‍🏫',
    description: t('aiAgents.professor.description', { ns: 'aiAgents' }),
    agents: [
      {
        name: t('aiAgents.professor.agents.contentCreator.name', { ns: 'aiAgents' }),
        icon: '✨',
        color: '#FF9800',
        description: t('aiAgents.professor.agents.contentCreator.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.professor.agents.contentCreator.keywords', { ns: 'aiAgents' }) || ['content', 'lesson', 'material'],
      },
      {
        name: t('aiAgents.professor.agents.assessmentDesigner.name', { ns: 'aiAgents' }),
        icon: '📊',
        color: '#F44336',
        description: t('aiAgents.professor.agents.assessmentDesigner.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.professor.agents.assessmentDesigner.keywords', { ns: 'aiAgents' }) || ['quiz', 'test', 'assessment'],
      },
      {
        name: t('aiAgents.professor.agents.curriculumPlanner.name', { ns: 'aiAgents' }),
        icon: '📋',
        color: '#2196F3',
        description: t('aiAgents.professor.agents.curriculumPlanner.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.professor.agents.curriculumPlanner.keywords', { ns: 'aiAgents' }) || ['curriculum', 'plan', 'structure'],
      },
      {
        name: t('aiAgents.professor.agents.scienceTutor.name', { ns: 'aiAgents' }),
        icon: '🔬',
        color: '#9C27B0',
        description: t('aiAgents.professor.agents.scienceTutor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.professor.agents.scienceTutor.keywords', { ns: 'aiAgents' }) || ['science', 'experiment', 'lab'],
      },
    ],
    sampleMessages: t('aiAgents.professor.sampleMessages', { ns: 'aiAgents' }) || [
      'Create a calculus quiz for intermediate students',
      'Generate lesson content on cellular biology',
      'Help me design an assessment for algebra',
      'Create practice problems for physics',
      'Develop a writing assignment rubric',
    ],
  },
  {
    name: t('aiAgents.admin.title', { ns: 'aiAgents' }),
    code: 'ADMIN',
    color: '#9C27B0',
    icon: '⚙️',
    description: t('aiAgents.admin.description', { ns: 'aiAgents' }),
    agents: [
      {
        name: t('aiAgents.admin.agents.dataAnalyst.name', { ns: 'aiAgents' }),
        icon: '📈',
        color: '#4CAF50',
        description: t('aiAgents.admin.agents.dataAnalyst.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.admin.agents.dataAnalyst.keywords', { ns: 'aiAgents' }) || ['data', 'analytics', 'reports'],
      },
      {
        name: t('aiAgents.admin.agents.systemManager.name', { ns: 'aiAgents' }),
        icon: '🔧',
        color: '#FF9800',
        description: t('aiAgents.admin.agents.systemManager.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.admin.agents.systemManager.keywords', { ns: 'aiAgents' }) || ['system', 'admin', 'management'],
      },
      {
        name: t('aiAgents.admin.agents.policyAdvisor.name', { ns: 'aiAgents' }),
        icon: '📋',
        color: '#2196F3',
        description: t('aiAgents.admin.agents.policyAdvisor.description', { ns: 'aiAgents' }),
        keywords: t('aiAgents.admin.agents.policyAdvisor.keywords', { ns: 'aiAgents' }) || ['policy', 'procedure', 'academic'],
      },
    ],
    sampleMessages: t('aiAgents.admin.sampleMessages', { ns: 'aiAgents' }) || [
      'Generate enrollment analytics report',
      'Help me create a new academic policy',
      'Analyze student performance trends',
      'Create system usage statistics',
      'Review course completion rates',
    ],
  },
];

/**
 * Get common AI agent messages with translations
 */
export const getCommonMessages = (t: any) => ({
  welcomeMessage: t('aiAgents.common.welcomeMessage', { ns: 'aiAgents' }),
  errorMessage: t('aiAgents.common.errorMessage', { ns: 'aiAgents' }),
  loadingMessage: t('aiAgents.common.loadingMessage', { ns: 'aiAgents' }),
  fallbackMessage: t('aiAgents.common.fallbackMessage', { ns: 'aiAgents' }),
  typeMessage: t('aiAgents.common.typeMessage', { ns: 'aiAgents' }),
  send: t('aiAgents.common.send', { ns: 'aiAgents' }),
  clear: t('aiAgents.common.clear', { ns: 'aiAgents' }),
  newConversation: t('aiAgents.common.newConversation', { ns: 'aiAgents' }),
  conversationHistory: t('aiAgents.common.conversationHistory', { ns: 'aiAgents' }),
  suggestions: t('aiAgents.common.suggestions', { ns: 'aiAgents' }),
  quickActions: t('aiAgents.common.quickActions', { ns: 'aiAgents' }),
});

/**
 * Get error messages with translations
 */
export const getErrorMessages = (t: any) => ({
  connectionFailed: t('aiAgents.errors.connectionFailed', { ns: 'aiAgents' }),
  requestTimeout: t('aiAgents.errors.requestTimeout', { ns: 'aiAgents' }),
  invalidResponse: t('aiAgents.errors.invalidResponse', { ns: 'aiAgents' }),
  serviceUnavailable: t('aiAgents.errors.serviceUnavailable', { ns: 'aiAgents' }),
  rateLimitExceeded: t('aiAgents.errors.rateLimitExceeded', { ns: 'aiAgents' }),
  authenticationRequired: t('aiAgents.errors.authenticationRequired', { ns: 'aiAgents' }),
  insufficientPermissions: t('aiAgents.errors.insufficientPermissions', { ns: 'aiAgents' }),
});

/**
 * Get status messages with translations
 */
export const getStatusMessages = (t: any) => ({
  online: t('aiAgents.status.online', { ns: 'aiAgents' }),
  offline: t('aiAgents.status.offline', { ns: 'aiAgents' }),
  connecting: t('aiAgents.status.connecting', { ns: 'aiAgents' }),
  reconnecting: t('aiAgents.status.reconnecting', { ns: 'aiAgents' }),
  healthy: t('aiAgents.status.healthy', { ns: 'aiAgents' }),
  degraded: t('aiAgents.status.degraded', { ns: 'aiAgents' }),
  unhealthy: t('aiAgents.status.unhealthy', { ns: 'aiAgents' }),
});
