/**
 * Tests for UnifiedAIService
 * 
 * Comprehensive test suite for the consolidated AI service functionality
 */

import { unifiedAIService } from '../UnifiedAIService';
import { AIServiceError } from '../../utils/aiServiceUtils';

// Mock the API endpoints
jest.mock('../../../config/api', () => ({
  API_ENDPOINTS: {
    UNIFIED_AI: {
      BASE: '/api/v1/ai',
    },
  },
}));

// Mock the BaseAIService
jest.mock('../../utils/BaseAIService', () => ({
  BaseAIService: jest.fn().mockImplementation(() => ({
    post: jest.fn(),
    get: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    updateConfig: jest.fn(),
    getConfig: jest.fn(() => ({})),
    serviceName: 'Test Service',
    baseEndpoint: '/api/v1/ai',
  })),
}));

describe('UnifiedAIService', () => {
  let mockPost: jest.Mock;
  let mockGet: jest.Mock;
  let mockPatch: jest.Mock;
  let mockDelete: jest.Mock;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Get references to the mocked methods
    mockPost = (unifiedAIService as any).post;
    mockGet = (unifiedAIService as any).get;
    mockPatch = (unifiedAIService as any).patch;
    mockDelete = (unifiedAIService as any).delete;
  });

  describe('Chat Functionality', () => {
    describe('sendMessage', () => {
      it('should send a message successfully', async () => {
        const mockResponse = {
          content: 'Test response',
          role: 'assistant',
          timestamp: new Date().toISOString(),
        };

        mockPost.mockResolvedValue(mockResponse);

        const result = await unifiedAIService.sendMessage('Hello', 1, {
          type: 'tutoring',
          courseId: 'course-123',
        });

        expect(mockPost).toHaveBeenCalledWith('chat/', {
          message: 'Hello',
          conversation_id: 1,
          context: {
            type: 'tutoring',
            course_id: 'course-123',
          },
        });

        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockResponse);
        expect(result.metadata?.model).toBe('unified-ai');
      });

      it('should handle errors with fallback enabled', async () => {
        mockPost.mockRejectedValue(new Error('Network error'));

        const result = await unifiedAIService.sendMessage('Hello');

        expect(result.status).toBe('success');
        expect(result.data.content).toContain('technical difficulties');
        expect(result.data.metadata?.fallback).toBe(true);
      });

      it('should use default context when none provided', async () => {
        const mockResponse = {
          content: 'Test response',
          role: 'assistant',
          timestamp: new Date().toISOString(),
        };

        mockPost.mockResolvedValue(mockResponse);

        await unifiedAIService.sendMessage('Hello');

        expect(mockPost).toHaveBeenCalledWith('chat/', {
          message: 'Hello',
          conversation_id: undefined,
          context: {
            type: 'general',
            course_id: undefined,
          },
        });
      });
    });

    describe('getConversations', () => {
      it('should fetch conversations successfully', async () => {
        const mockConversations = [
          {
            id: 1,
            title: 'Test Conversation',
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
            message_count: 5,
          },
        ];

        mockGet.mockResolvedValue(mockConversations);

        const result = await unifiedAIService.getConversations();

        expect(mockGet).toHaveBeenCalledWith('conversations/');
        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockConversations);
      });

      it('should handle errors with fallback', async () => {
        mockGet.mockRejectedValue(new Error('Network error'));

        const result = await unifiedAIService.getConversations();

        expect(result.status).toBe('success');
        expect(result.data).toEqual([]);
        expect(result.message).toBe('Using cached conversations');
      });
    });

    describe('deleteConversation', () => {
      it('should delete conversation successfully', async () => {
        mockDelete.mockResolvedValue(undefined);

        const result = await unifiedAIService.deleteConversation(1);

        expect(mockDelete).toHaveBeenCalledWith('conversations/1/');
        expect(result.status).toBe('success');
        expect(result.message).toBe('Conversation deleted successfully');
      });

      it('should throw AIServiceError on failure', async () => {
        mockDelete.mockRejectedValue(new Error('Delete failed'));

        await expect(unifiedAIService.deleteConversation(1))
          .rejects.toThrow(AIServiceError);
      });
    });
  });

  describe('Tutoring Functionality', () => {
    describe('askTutoringQuestion', () => {
      it('should ask tutoring question successfully', async () => {
        const mockResponse = {
          content: 'Math explanation',
          confidence: 0.9,
        };

        mockPost.mockResolvedValue(mockResponse);

        const request = {
          question: 'Explain quadratic equations',
          subject: 'mathematics',
          studentLevel: 'intermediate',
        };

        const result = await unifiedAIService.askTutoringQuestion(request);

        expect(mockPost).toHaveBeenCalledWith('tutoring/', request);
        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockResponse);
        expect(result.metadata?.confidence).toBe(0.9);
        expect(result.metadata?.subject).toBe('mathematics');
      });

      it('should provide fallback response on error', async () => {
        mockPost.mockRejectedValue(new Error('Service error'));

        const request = {
          question: 'Explain quadratic equations',
        };

        const result = await unifiedAIService.askTutoringQuestion(request);

        expect(result.status).toBe('success');
        expect(result.data.content).toContain('trouble processing');
        expect(result.message).toBe('Fallback tutoring response');
      });
    });

    describe('askQuestion (legacy compatibility)', () => {
      it('should maintain backward compatibility', async () => {
        const mockResponse = {
          data: {
            content: 'Legacy response',
          },
          metadata: {
            confidence: 0.8,
            processingTime: 1500,
          },
        };

        // Mock the askTutoringQuestion method
        jest.spyOn(unifiedAIService, 'askTutoringQuestion')
          .mockResolvedValue(mockResponse);

        const result = await unifiedAIService.askQuestion('Test question', {
          subject: 'math',
          level: 'beginner',
          sessionId: 'session-123',
        });

        expect(result.answer).toBe('Legacy response');
        expect(result.confidence_score).toBe(0.8);
        expect(result.response_time).toBe(1500);
        expect(result.session_id).toBe('session-123');
      });
    });
  });

  describe('Study Assistant Functionality', () => {
    describe('getStudySessions', () => {
      it('should fetch study sessions successfully', async () => {
        const mockSessions = [
          {
            id: 1,
            course_id: 1,
            start_time: '2024-01-01T10:00:00Z',
            topics_covered: ['algebra', 'geometry'],
          },
        ];

        mockGet.mockResolvedValue(mockSessions);

        const result = await unifiedAIService.getStudySessions();

        expect(mockGet).toHaveBeenCalledWith('study/sessions/');
        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockSessions);
      });
    });

    describe('startStudySession', () => {
      it('should start study session successfully', async () => {
        const mockSession = {
          id: 1,
          course_id: 1,
          start_time: '2024-01-01T10:00:00Z',
          topics_covered: ['algebra'],
        };

        mockPost.mockResolvedValue(mockSession);

        const result = await unifiedAIService.startStudySession(1, ['algebra']);

        expect(mockPost).toHaveBeenCalledWith('study/sessions/', {
          course_id: 1,
          topics_covered: ['algebra'],
          start_time: expect.any(String),
        });

        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockSession);
        expect(result.message).toBe('Study session started successfully');
      });
    });

    describe('generateStudyPlan', () => {
      it('should generate study plan successfully', async () => {
        const mockPlan = {
          id: 1,
          course_id: 1,
          total_duration: '8 weeks',
          weekly_schedule: [
            { week: 1, topics: ['Introduction'], hours: 3 },
          ],
          milestones: [
            { week: 4, description: 'Complete basics' },
          ],
        };

        mockPost.mockResolvedValue(mockPlan);

        const result = await unifiedAIService.generateStudyPlan(1);

        expect(mockPost).toHaveBeenCalledWith('study/generate-plan/', {
          course_id: 1,
        });

        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockPlan);
      });

      it('should provide fallback plan on error', async () => {
        mockPost.mockRejectedValue(new Error('Service error'));

        const result = await unifiedAIService.generateStudyPlan(1);

        expect(result.status).toBe('success');
        expect(result.data.course_id).toBe(1);
        expect(result.data.total_duration).toBe('8 weeks');
        expect(result.message).toBe('Fallback study plan provided');
      });
    });
  });

  describe('Assessment Functionality', () => {
    describe('generateAssessment', () => {
      it('should generate assessment successfully', async () => {
        const mockAssessment = {
          questions: [
            {
              id: 1,
              question: 'What is 2+2?',
              options: ['3', '4', '5', '6'],
              correct_answer: '4',
            },
          ],
        };

        mockPost.mockResolvedValue(mockAssessment);

        const request = {
          type: 'quiz',
          subject: 'mathematics',
          difficulty: 'easy',
          questionCount: 5,
        };

        const result = await unifiedAIService.generateAssessment(request);

        expect(mockPost).toHaveBeenCalledWith('assessment/generate/', request);
        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockAssessment);
      });
    });

    describe('checkAnswer', () => {
      it('should check answer successfully', async () => {
        const mockResult = {
          correct: true,
          explanation: 'Correct! 2+2 equals 4.',
          score: 1,
        };

        mockPost.mockResolvedValue(mockResult);

        const result = await unifiedAIService.checkAnswer(1, '4');

        expect(mockPost).toHaveBeenCalledWith('assessment/check-answer/', {
          question_id: 1,
          student_answer: '4',
        });

        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockResult);
      });
    });
  });

  describe('Service Management', () => {
    describe('getCapabilities', () => {
      it('should return service capabilities', () => {
        const capabilities = unifiedAIService.getCapabilities();

        expect(capabilities).toEqual({
          chat: true,
          tutoring: true,
          studyPlanning: true,
          contentGeneration: true,
          assessment: true,
          analytics: true,
        });
      });
    });

    describe('checkHealth', () => {
      it('should check service health successfully', async () => {
        const mockHealth = {
          status: 'healthy',
          uptime: '99.9%',
          response_time: '150ms',
        };

        mockGet.mockResolvedValue(mockHealth);

        const result = await unifiedAIService.checkHealth();

        expect(mockGet).toHaveBeenCalledWith('health/');
        expect(result.status).toBe('success');
        expect(result.data).toEqual(mockHealth);
      });

      it('should handle health check failure', async () => {
        mockGet.mockRejectedValue(new Error('Health check failed'));

        const result = await unifiedAIService.checkHealth();

        expect(result.status).toBe('error');
        expect(result.data.status).toBe('unhealthy');
        expect(result.message).toBe('Health check failed');
      });
    });
  });
});
