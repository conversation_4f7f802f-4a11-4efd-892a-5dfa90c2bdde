{"en": {"aiAgents": {"student": {"title": "Student AI Assistants", "description": "Get personalized help with your studies", "agents": {"mathTutor": {"name": "<PERSON>", "description": "Mathematics and problem solving", "keywords": ["math", "algebra", "calculus", "geometry"]}, "scienceTutor": {"name": "Science Tutor", "description": "Science concepts and experiments", "keywords": ["science", "physics", "chemistry", "biology"]}, "languageTutor": {"name": "Language Tutor", "description": "Writing and language arts", "keywords": ["write", "essay", "grammar"]}, "careerAdvisor": {"name": "Career Advisor", "description": "Academic and career guidance", "keywords": ["career", "advice", "guidance"]}}, "sampleMessages": ["Help me understand quadratic equations", "Explain photosynthesis process", "How do I write a better essay?", "What career path should I choose?", "I need help with my homework"]}, "professor": {"title": "Professor AI Assistants", "description": "Create content, assessments, and manage curriculum", "agents": {"contentCreator": {"name": "Content Creator", "description": "Course content and materials", "keywords": ["content", "lesson", "material"]}, "assessmentDesigner": {"name": "Assessment Designer", "description": "Quizzes and assessments", "keywords": ["quiz", "test", "assessment"]}, "curriculumPlanner": {"name": "Curriculum Planner", "description": "Course structure and planning", "keywords": ["curriculum", "plan", "structure"]}, "scienceTutor": {"name": "Science Tutor", "description": "Science lesson planning", "keywords": ["science", "experiment", "lab"]}}, "sampleMessages": ["Create a calculus quiz for intermediate students", "Generate lesson content on cellular biology", "Help me design an assessment for algebra", "Create practice problems for physics", "Develop a writing assignment rubric"]}, "admin": {"title": "Admin AI Assistants", "description": "Manage university operations and analytics", "agents": {"dataAnalyst": {"name": "Data Analyst", "description": "University analytics and insights", "keywords": ["data", "analytics", "reports"]}, "systemManager": {"name": "System Manager", "description": "System administration and management", "keywords": ["system", "admin", "management"]}, "policyAdvisor": {"name": "Policy Advisor", "description": "Academic policies and procedures", "keywords": ["policy", "procedure", "academic"]}}, "sampleMessages": ["Generate enrollment analytics report", "Help me create a new academic policy", "Analyze student performance trends", "Create system usage statistics", "Review course completion rates"]}, "common": {"welcomeMessage": "Hello! How can I help you today?", "errorMessage": "I'm sorry, I encountered an error. Please try again.", "loadingMessage": "Processing your request...", "fallbackMessage": "I'm currently experiencing some technical difficulties. Please try again in a moment.", "typeMessage": "Type your message...", "send": "Send", "clear": "Clear", "newConversation": "New Conversation", "conversationHistory": "Conversation History", "suggestions": "Suggestions", "quickActions": "Quick Actions"}, "errors": {"connectionFailed": "Failed to connect to AI service", "requestTimeout": "Request timed out. Please try again.", "invalidResponse": "Received invalid response from AI service", "serviceUnavailable": "AI service is currently unavailable", "rateLimitExceeded": "Too many requests. Please wait a moment.", "authenticationRequired": "Please log in to access AI features", "insufficientPermissions": "You don't have permission to access this feature"}, "status": {"online": "Online", "offline": "Offline", "connecting": "Connecting...", "reconnecting": "Reconnecting...", "healthy": "Service is healthy", "degraded": "Service is experiencing issues", "unhealthy": "Service is unavailable"}}}, "ar": {"aiAgents": {"student": {"title": "مساعدي الذكاء الاصطناعي للطلاب", "description": "احصل على مساعدة شخصية في دراستك", "agents": {"mathTutor": {"name": "مدرس الرياضيات", "description": "الرياضيات وحل المشاكل", "keywords": ["رياضيات", "<PERSON><PERSON><PERSON>", "حساب التفاضل", "هندسة"]}, "scienceTutor": {"name": "مدرس العلوم", "description": "المفاهيم العلمية والتجارب", "keywords": ["علوم", "فيزياء", "كيمياء", "أحياء"]}, "languageTutor": {"name": "مدرس اللغة", "description": "الكتابة وفنون اللغة", "keywords": ["كتابة", "مقال", "قواعد"]}, "careerAdvisor": {"name": "مستشار مهني", "description": "التوجيه الأكاديمي والمهني", "keywords": ["مهنة", "نصيحة", "توجيه"]}}, "sampleMessages": ["ساعدني في فهم المعادلات التربيعية", "اشرح لي عملية التمثيل الضوئي", "كيف أكتب مقالاً أفضل؟", "ما المسار المهني الذي يجب أن أختاره؟", "أحتاج مساعدة في واجبي المنزلي"]}, "professor": {"title": "مساعدي الذكاء الاصطناعي للأساتذة", "description": "إنشاء المحتوى والتقييمات وإدارة المناهج", "agents": {"contentCreator": {"name": "منش<PERSON> المحتوى", "description": "محتوى الدورة والمواد", "keywords": ["مح<PERSON><PERSON><PERSON>", "درس", "مادة"]}, "assessmentDesigner": {"name": "مصمم التقييم", "description": "الاختبارات والتقييمات", "keywords": ["اختبار", "امتحان", "تقييم"]}, "curriculumPlanner": {"name": "مخ<PERSON><PERSON> المناهج", "description": "هيكل الدورة والتخطيط", "keywords": ["منهج", "خطة", "هيكل"]}, "scienceTutor": {"name": "مدرس العلوم", "description": "تخطيط دروس العلوم", "keywords": ["علوم", "تجربة", "مختبر"]}}, "sampleMessages": ["أنشئ اختبار حساب التفاضل للطلاب المتوسطين", "أنتج محتوى درس عن علم الأحياء الخلوي", "ساعدني في تصميم تقييم للجبر", "أنشئ مسائل تدريبية للفيزياء", "طور معايير تقييم مهمة الكتابة"]}, "admin": {"title": "مساعدي الذكاء الاصطناعي للإدارة", "description": "إدارة عمليات الجامعة والتحليلات", "agents": {"dataAnalyst": {"name": "م<PERSON><PERSON><PERSON> البيانات", "description": "تحليلات الجامعة والرؤى", "keywords": ["بيانات", "تحليلات", "تقارير"]}, "systemManager": {"name": "مدير النظام", "description": "إدارة النظام والإدارة", "keywords": ["نظام", "إدارة", "إدارة"]}, "policyAdvisor": {"name": "مستشار السياسات", "description": "السياسات والإجراءات الأكاديمية", "keywords": ["سياسة", "إجراء", "أكاديمي"]}}, "sampleMessages": ["أنتج تقرير تحليلات التسجيل", "ساعدني في إنشاء سياسة أكاديمية جديدة", "حلل اتجاهات أداء الطلاب", "أنشئ إحصائيات استخدام النظام", "راجع معدلات إكمال الدورات"]}, "common": {"welcomeMessage": "مرحباً! كيف يمكنني مساعدتك اليوم؟", "errorMessage": "آسف، واجهت خطأ. يرجى المحاولة مرة أخرى.", "loadingMessage": "جاري معالجة طلبك...", "fallbackMessage": "أواجه حالياً بعض الصعوبات التقنية. يرجى المحاولة مرة أخرى خلال لحظة.", "typeMessage": "اكتب رسالتك...", "send": "إرسال", "clear": "م<PERSON><PERSON>", "newConversation": "محاد<PERSON>ة جديدة", "conversationHistory": "تاريخ المحادثة", "suggestions": "اقتراحات", "quickActions": "إجراءات سريعة"}, "errors": {"connectionFailed": "فشل في الاتصال بخدمة الذكاء الاصطناعي", "requestTimeout": "انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.", "invalidResponse": "تم استلام استجابة غير صالحة من خدمة الذكاء الاصطناعي", "serviceUnavailable": "خدمة الذكاء الاصطناعي غير متاحة حالياً", "rateLimitExceeded": "طلبات كثيرة جداً. يرجى الانتظار لحظة.", "authenticationRequired": "يرجى تسجيل الدخول للوصول إلى ميزات الذكاء الاصطناعي", "insufficientPermissions": "ليس لديك إذن للوصول إلى هذه الميزة"}, "status": {"online": "متصل", "offline": "<PERSON>ير متصل", "connecting": "جاري الاتصال...", "reconnecting": "جاري إعادة الاتصال...", "healthy": "الخدمة تعمل بشكل جيد", "degraded": "الخدمة تواجه مشاكل", "unhealthy": "الخدمة غير متاحة"}}}}