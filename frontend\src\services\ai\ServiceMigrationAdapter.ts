/**
 * Service Migration Adapter
 * 
 * Provides backward compatibility for existing code that uses the old service interfaces.
 * This allows for gradual migration to the new unified service.
 */

import { unifiedAIService } from './UnifiedAIService';
import { BaseAIService } from '../utils/BaseAIService';

// ==========================================
// LEGACY SERVICE ADAPTERS
// ==========================================

/**
 * AI Assistant Service Adapter
 * Maintains compatibility with existing aiAssistantService usage
 */
class AIAssistantServiceAdapter extends BaseAIService {
  constructor() {
    super({
      serviceName: 'AI Assistant (Legacy Adapter)',
      baseEndpoint: '/api/v1/ai/assistant',
      config: {
        timeout: 30000,
        retries: 2,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 300000,
      },
    });
  }

  /**
   * Legacy askQuestion method - delegates to unified service
   */
  async askQuestion(question: string, context: any = {}): Promise<any> {
    console.warn('DEPRECATED: aiAssistantService.askQuestion() - Use unifiedAIService.askTutoringQuestion() instead');
    return await unifiedAIService.askQuestion(question, context);
  }

  /**
   * Legacy getSuggestions method
   */
  async getSuggestions(): Promise<any[]> {
    console.warn('DEPRECATED: aiAssistantService.getSuggestions() - Use unifiedAIService.getAnalytics() instead');
    try {
      const response = await unifiedAIService.getAnalytics();
      return response.data.suggestions || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Legacy getAnalytics method
   */
  async getAnalytics(): Promise<any> {
    console.warn('DEPRECATED: aiAssistantService.getAnalytics() - Use unifiedAIService.getAnalytics() instead');
    const response = await unifiedAIService.getAnalytics();
    return response.data;
  }
}

/**
 * Chatbot Service Adapter
 * Maintains compatibility with existing chatbotService usage
 */
class ChatbotServiceAdapter extends BaseAIService {
  constructor() {
    super({
      serviceName: 'Chatbot (Legacy Adapter)',
      baseEndpoint: '/api/v1/chatbot',
      config: {
        timeout: 45000,
        retries: 1,
        fallbackEnabled: true,
        cacheEnabled: false,
        cacheTTL: 0,
      },
    });
  }

  /**
   * Legacy sendMessage method - delegates to unified service
   */
  async sendMessage(message: string, conversationId?: number): Promise<any> {
    console.warn('DEPRECATED: chatbotService.sendMessage() - Use unifiedAIService.sendMessage() instead');
    const response = await unifiedAIService.sendMessage(message, conversationId, { type: 'general' });
    
    // Return in legacy format
    return {
      message: response.data.content,
      conversation_id: conversationId,
      timestamp: response.data.timestamp,
      metadata: response.metadata,
    };
  }

  /**
   * Legacy getConversations method
   */
  async getConversations(): Promise<any[]> {
    console.warn('DEPRECATED: chatbotService.getConversations() - Use unifiedAIService.getConversations() instead');
    const response = await unifiedAIService.getConversations();
    return response.data;
  }

  /**
   * Legacy getConversation method
   */
  async getConversation(id: number): Promise<any> {
    console.warn('DEPRECATED: chatbotService.getConversation() - Use unifiedAIService.getConversation() instead');
    const response = await unifiedAIService.getConversation(id);
    return response.data;
  }

  /**
   * Legacy deleteConversation method
   */
  async deleteConversation(id: number): Promise<void> {
    console.warn('DEPRECATED: chatbotService.deleteConversation() - Use unifiedAIService.deleteConversation() instead');
    await unifiedAIService.deleteConversation(id);
  }

  /**
   * Legacy updateConversationTitle method
   */
  async updateConversationTitle(id: number, title: string): Promise<any> {
    console.warn('DEPRECATED: chatbotService.updateConversationTitle() - Feature moved to unified service');
    // This would need to be implemented in the unified service if needed
    throw new Error('updateConversationTitle is deprecated - please use unified service');
  }

  /**
   * Legacy getMessages method
   */
  async getMessages(id: number): Promise<any[]> {
    console.warn('DEPRECATED: chatbotService.getMessages() - Feature moved to unified service');
    // This would need to be implemented in the unified service if needed
    throw new Error('getMessages is deprecated - please use unified service');
  }
}

/**
 * Study Assistant Service Adapter
 * Maintains compatibility with existing studyAssistantService usage
 */
class StudyAssistantServiceAdapter extends BaseAIService {
  constructor() {
    super({
      serviceName: 'Study Assistant (Legacy Adapter)',
      baseEndpoint: '/api/v1/study-assistant',
      config: {
        timeout: 30000,
        retries: 2,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 300000,
      },
    });
  }

  /**
   * Legacy getStudySessions method
   */
  async getStudySessions(): Promise<any[]> {
    console.warn('DEPRECATED: studyAssistantService.getStudySessions() - Use unifiedAIService.getStudySessions() instead');
    const response = await unifiedAIService.getStudySessions();
    return response.data;
  }

  /**
   * Legacy startStudySession method
   */
  async startStudySession(courseId: number, topics: string[]): Promise<any> {
    console.warn('DEPRECATED: studyAssistantService.startStudySession() - Use unifiedAIService.startStudySession() instead');
    const response = await unifiedAIService.startStudySession(courseId, topics);
    return response.data;
  }

  /**
   * Legacy endStudySession method
   */
  async endStudySession(sessionId: number, effectivenessScore?: number): Promise<any> {
    console.warn('DEPRECATED: studyAssistantService.endStudySession() - Use unifiedAIService.endStudySession() instead');
    const response = await unifiedAIService.endStudySession(sessionId, effectivenessScore);
    return response.data;
  }

  /**
   * Legacy generateStudyPlan method
   */
  async generateStudyPlan(courseId: number): Promise<any> {
    console.warn('DEPRECATED: studyAssistantService.generateStudyPlan() - Use unifiedAIService.generateStudyPlan() instead');
    const response = await unifiedAIService.generateStudyPlan(courseId);
    return response.data;
  }

  /**
   * Legacy getStudyTopics method
   */
  async getStudyTopics(): Promise<any[]> {
    console.warn('DEPRECATED: studyAssistantService.getStudyTopics() - Feature moved to unified service');
    // This would need to be implemented in the unified service if needed
    return [];
  }

  /**
   * Legacy getRecommendations method
   */
  async getRecommendations(): Promise<any[]> {
    console.warn('DEPRECATED: studyAssistantService.getRecommendations() - Use unifiedAIService.getAnalytics() instead');
    try {
      const response = await unifiedAIService.getAnalytics();
      return response.data.recommendations || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Legacy markRecommendationImplemented method
   */
  async markRecommendationImplemented(id: number): Promise<void> {
    console.warn('DEPRECATED: studyAssistantService.markRecommendationImplemented() - Feature moved to unified service');
    // This would need to be implemented in the unified service if needed
  }

  /**
   * Legacy checkAnswer method
   */
  async checkAnswer(questionId: number, studentAnswer: string): Promise<any> {
    console.warn('DEPRECATED: studyAssistantService.checkAnswer() - Use unifiedAIService.checkAnswer() instead');
    const response = await unifiedAIService.checkAnswer(questionId, studentAnswer);
    return response.data;
  }

  /**
   * Legacy getMyQuestions method
   */
  async getMyQuestions(): Promise<any[]> {
    console.warn('DEPRECATED: studyAssistantService.getMyQuestions() - Feature moved to unified service');
    // This would need to be implemented in the unified service if needed
    return [];
  }
}

// ==========================================
// ADAPTER INSTANCES
// ==========================================

export const aiAssistantServiceAdapter = new AIAssistantServiceAdapter();
export const chatbotServiceAdapter = new ChatbotServiceAdapter();
export const studyAssistantServiceAdapter = new StudyAssistantServiceAdapter();

// ==========================================
// MIGRATION UTILITIES
// ==========================================

/**
 * Migration utility to help developers transition to the unified service
 */
export class ServiceMigrationHelper {
  /**
   * Log migration warnings for deprecated service usage
   */
  static logMigrationWarning(oldService: string, oldMethod: string, newMethod: string): void {
    console.group(`🔄 Service Migration Warning`);
    console.warn(`The ${oldService}.${oldMethod}() method is deprecated.`);
    console.info(`Please migrate to: unifiedAIService.${newMethod}()`);
    console.info(`See migration guide: /docs/service-migration.md`);
    console.groupEnd();
  }

  /**
   * Check if all services have been migrated
   */
  static checkMigrationStatus(): { migrated: boolean; warnings: string[] } {
    const warnings: string[] = [];
    
    // This would be expanded to check actual usage patterns
    // For now, it's a placeholder for future implementation
    
    return {
      migrated: warnings.length === 0,
      warnings,
    };
  }

  /**
   * Get migration recommendations
   */
  static getMigrationRecommendations(): string[] {
    return [
      'Replace aiAssistantService.askQuestion() with unifiedAIService.askTutoringQuestion()',
      'Replace chatbotService.sendMessage() with unifiedAIService.sendMessage()',
      'Replace studyAssistantService methods with equivalent unifiedAIService methods',
      'Update import statements to use unifiedAIService',
      'Remove unused legacy service imports',
    ];
  }
}

export default {
  aiAssistantServiceAdapter,
  chatbotServiceAdapter,
  studyAssistantServiceAdapter,
  ServiceMigrationHelper,
};
