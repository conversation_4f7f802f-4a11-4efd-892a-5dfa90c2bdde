/**
 * Unified AI Service - Consolidated AI functionality
 * 
 * This service consolidates all AI-related functionality from multiple services:
 * - aiAssistantService
 * - chatbotService  
 * - studyAssistantService
 * - unifiedAiService
 * 
 * Provides a single, consistent interface for all AI operations.
 */

import { API_ENDPOINTS } from '../../config/api';
import { BaseAIService } from '../utils/BaseAIService';
import { AIServiceError } from '../utils/aiServiceUtils';

// ==========================================
// UNIFIED TYPES & INTERFACES
// ==========================================

export interface AICapabilities {
  chat: boolean;
  tutoring: boolean;
  studyPlanning: boolean;
  contentGeneration: boolean;
  assessment: boolean;
  analytics: boolean;
}

export interface ChatMessage {
  id?: number;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ChatConversation {
  id: number;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
  last_message?: ChatMessage;
}

export interface StudySession {
  id: number;
  course_id: number;
  start_time: string;
  end_time?: string;
  duration?: number;
  topics_covered: string[];
  effectiveness_score?: number;
}

export interface StudyPlan {
  id: number;
  course_id: number;
  total_duration: string;
  weekly_schedule: Array<{
    week: number;
    topics: string[];
    hours: number;
  }>;
  milestones: Array<{
    week: number;
    description: string;
  }>;
}

export interface AIResponse<T = any> {
  data: T;
  status: 'success' | 'error';
  message?: string;
  metadata?: {
    confidence?: number;
    processingTime?: number;
    model?: string;
    [key: string]: any;
  };
}

export interface TutoringRequest {
  question: string;
  subject?: string;
  studentLevel?: string;
  context?: Record<string, any>;
}

export interface AssessmentRequest {
  type: string;
  subject: string;
  difficulty: string;
  questionCount?: number;
  learningObjectives?: string[];
}

// ==========================================
// UNIFIED AI SERVICE CLASS
// ==========================================

class UnifiedAIService extends BaseAIService {
  private capabilities: AICapabilities;
  private healthStatus: Map<string, any> = new Map();

  constructor() {
    super({
      serviceName: 'Unified AI Service',
      baseEndpoint: API_ENDPOINTS.UNIFIED_AI?.BASE || '/api/v1/ai',
      config: {
        timeout: 45000,
        retries: 3,
        fallbackEnabled: true,
        cacheEnabled: true,
        cacheTTL: 600000, // 10 minutes
      },
    });

    this.capabilities = {
      chat: true,
      tutoring: true,
      studyPlanning: true,
      contentGeneration: true,
      assessment: true,
      analytics: true,
    };
  }

  // ==========================================
  // CORE CHAT FUNCTIONALITY
  // ==========================================

  /**
   * Send a message to the AI assistant
   * Consolidates chatbot and AI assistant functionality
   */
  async sendMessage(
    message: string,
    conversationId?: number,
    context?: {
      type?: 'general' | 'tutoring' | 'study' | 'support';
      courseId?: string;
      metadata?: any;
    }
  ): Promise<AIResponse<ChatMessage>> {
    try {
      const payload = {
        message,
        conversation_id: conversationId,
        context: {
          type: context?.type || 'general',
          course_id: context?.courseId,
          ...context?.metadata,
        },
      };

      const response = await this.post<ChatMessage>('chat/', payload);
      
      return {
        data: response,
        status: 'success',
        metadata: {
          processingTime: Date.now(),
          model: 'unified-ai',
        },
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return this.getFallbackChatResponse(message);
      }
      throw error;
    }
  }

  /**
   * Get chat conversations for the current user
   */
  async getConversations(): Promise<AIResponse<ChatConversation[]>> {
    try {
      const conversations = await this.get<ChatConversation[]>('conversations/');
      return {
        data: conversations,
        status: 'success',
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return {
          data: [],
          status: 'success',
          message: 'Using cached conversations',
        };
      }
      throw error;
    }
  }

  /**
   * Get a specific conversation
   */
  async getConversation(id: number): Promise<AIResponse<ChatConversation>> {
    try {
      const conversation = await this.get<ChatConversation>(`conversations/${id}/`);
      return {
        data: conversation,
        status: 'success',
      };
    } catch (error) {
      throw new AIServiceError(`Failed to get conversation ${id}`, 'CONVERSATION_ERROR');
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(id: number): Promise<AIResponse<void>> {
    try {
      await this.delete(`conversations/${id}/`);
      return {
        data: undefined,
        status: 'success',
        message: 'Conversation deleted successfully',
      };
    } catch (error) {
      throw new AIServiceError(`Failed to delete conversation ${id}`, 'DELETE_ERROR');
    }
  }

  // ==========================================
  // TUTORING FUNCTIONALITY
  // ==========================================

  /**
   * Ask a tutoring question with enhanced context
   */
  async askTutoringQuestion(request: TutoringRequest): Promise<AIResponse<any>> {
    try {
      const response = await this.post('tutoring/', request);
      return {
        data: response,
        status: 'success',
        metadata: {
          confidence: response.confidence || 0.8,
          subject: request.subject,
        },
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return this.getFallbackTutoringResponse(request.question);
      }
      throw error;
    }
  }

  /**
   * Legacy compatibility method for AI Assistant
   */
  async askQuestion(question: string, context: any = {}): Promise<any> {
    const response = await this.askTutoringQuestion({
      question,
      subject: context.subject,
      studentLevel: context.level,
      context,
    });

    // Return in legacy format for backward compatibility
    return {
      answer: response.data.content || response.data.answer,
      confidence_score: response.metadata?.confidence || 0.8,
      response_time: response.metadata?.processingTime || 1000,
      session_id: context.sessionId || 'unified-session',
    };
  }

  // ==========================================
  // STUDY ASSISTANT FUNCTIONALITY
  // ==========================================

  /**
   * Get study sessions for the current user
   */
  async getStudySessions(): Promise<AIResponse<StudySession[]>> {
    try {
      const sessions = await this.get<StudySession[]>('study/sessions/');
      return {
        data: sessions,
        status: 'success',
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return {
          data: [],
          status: 'success',
          message: 'No study sessions available',
        };
      }
      throw error;
    }
  }

  /**
   * Start a new study session
   */
  async startStudySession(courseId: number, topics: string[]): Promise<AIResponse<StudySession>> {
    try {
      const session = await this.post<StudySession>('study/sessions/', {
        course_id: courseId,
        topics_covered: topics,
        start_time: new Date().toISOString(),
      });

      return {
        data: session,
        status: 'success',
        message: 'Study session started successfully',
      };
    } catch (error) {
      throw new AIServiceError('Failed to start study session', 'STUDY_SESSION_ERROR');
    }
  }

  /**
   * End a study session
   */
  async endStudySession(sessionId: number, effectivenessScore?: number): Promise<AIResponse<StudySession>> {
    try {
      const session = await this.patch<StudySession>(`study/sessions/${sessionId}/`, {
        end_time: new Date().toISOString(),
        effectiveness_score: effectivenessScore,
      });

      return {
        data: session,
        status: 'success',
        message: 'Study session ended successfully',
      };
    } catch (error) {
      throw new AIServiceError('Failed to end study session', 'STUDY_SESSION_ERROR');
    }
  }

  /**
   * Generate a study plan for a course
   */
  async generateStudyPlan(courseId: number): Promise<AIResponse<StudyPlan>> {
    try {
      const plan = await this.post<StudyPlan>('study/generate-plan/', {
        course_id: courseId,
      });

      return {
        data: plan,
        status: 'success',
        message: 'Study plan generated successfully',
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return this.getFallbackStudyPlan(courseId);
      }
      throw error;
    }
  }

  // ==========================================
  // ASSESSMENT FUNCTIONALITY
  // ==========================================

  /**
   * Generate assessment questions
   */
  async generateAssessment(request: AssessmentRequest): Promise<AIResponse<any>> {
    try {
      const assessment = await this.post('assessment/generate/', request);
      return {
        data: assessment,
        status: 'success',
        message: 'Assessment generated successfully',
      };
    } catch (error) {
      throw new AIServiceError('Failed to generate assessment', 'ASSESSMENT_ERROR');
    }
  }

  /**
   * Check a student's answer
   */
  async checkAnswer(questionId: number, studentAnswer: string): Promise<AIResponse<any>> {
    try {
      const result = await this.post('assessment/check-answer/', {
        question_id: questionId,
        student_answer: studentAnswer,
      });

      return {
        data: result,
        status: 'success',
      };
    } catch (error) {
      throw new AIServiceError('Failed to check answer', 'ANSWER_CHECK_ERROR');
    }
  }

  // ==========================================
  // CONTENT GENERATION
  // ==========================================

  /**
   * Generate course content
   */
  async generateContent(prompt: string, type: string = 'general'): Promise<AIResponse<any>> {
    try {
      const content = await this.post('content/generate/', {
        prompt,
        type,
      });

      return {
        data: content,
        status: 'success',
        message: 'Content generated successfully',
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return {
          data: { content: 'Content generation is temporarily unavailable.' },
          status: 'success',
          message: 'Fallback content provided',
        };
      }
      throw error;
    }
  }

  // ==========================================
  // ANALYTICS & INSIGHTS
  // ==========================================

  /**
   * Get AI analytics and insights
   */
  async getAnalytics(): Promise<AIResponse<any>> {
    try {
      const analytics = await this.get('analytics/');
      return {
        data: analytics,
        status: 'success',
      };
    } catch (error) {
      if (this.config.fallbackEnabled) {
        return {
          data: { message: 'Analytics temporarily unavailable' },
          status: 'success',
          message: 'Fallback analytics response',
        };
      }
      throw error;
    }
  }

  // ==========================================
  // FALLBACK METHODS
  // ==========================================

  private getFallbackChatResponse(message: string): AIResponse<ChatMessage> {
    return {
      data: {
        content: "I'm currently experiencing some technical difficulties. Please try again in a moment.",
        role: 'assistant',
        timestamp: new Date().toISOString(),
        metadata: { fallback: true },
      },
      status: 'success',
      message: 'Fallback response provided',
    };
  }

  private getFallbackTutoringResponse(question: string): AIResponse<any> {
    return {
      data: {
        content: "I'm having trouble processing your question right now. Please try rephrasing it or try again later.",
        confidence: 0.5,
      },
      status: 'success',
      message: 'Fallback tutoring response',
    };
  }

  private getFallbackStudyPlan(courseId: number): AIResponse<StudyPlan> {
    return {
      data: {
        id: 0,
        course_id: courseId,
        total_duration: '8 weeks',
        weekly_schedule: [
          { week: 1, topics: ['Introduction'], hours: 3 },
          { week: 2, topics: ['Fundamentals'], hours: 4 },
        ],
        milestones: [
          { week: 4, description: 'Complete basic concepts' },
          { week: 8, description: 'Ready for advanced topics' },
        ],
      },
      status: 'success',
      message: 'Fallback study plan provided',
    };
  }

  // ==========================================
  // SERVICE MANAGEMENT
  // ==========================================

  /**
   * Get service capabilities
   */
  getCapabilities(): AICapabilities {
    return { ...this.capabilities };
  }

  /**
   * Check service health
   */
  async checkHealth(): Promise<AIResponse<any>> {
    try {
      const health = await this.get('health/');
      return {
        data: health,
        status: 'success',
      };
    } catch (error) {
      return {
        data: { status: 'unhealthy', error: error.message },
        status: 'error',
        message: 'Health check failed',
      };
    }
  }

  /**
   * Update service configuration
   */
  updateServiceConfig(newConfig: Partial<any>): void {
    this.updateConfig(newConfig);
  }

  /**
   * Get service information
   */
  getServiceInfo(): any {
    return {
      name: this.serviceName,
      endpoint: this.baseEndpoint,
      capabilities: this.capabilities,
      config: this.getConfig(),
    };
  }
}

// Export singleton instance
export const unifiedAIService = new UnifiedAIService();
export default unifiedAIService;
