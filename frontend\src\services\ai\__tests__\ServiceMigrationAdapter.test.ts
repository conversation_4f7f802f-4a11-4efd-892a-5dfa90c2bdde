/**
 * Tests for Service Migration Adapters
 * 
 * Tests backward compatibility adapters for legacy service interfaces
 */

import {
  aiAssistantServiceAdapter,
  chatbotServiceAdapter,
  studyAssistantServiceAdapter,
  ServiceMigrationHelper,
} from '../ServiceMigrationAdapter';
import { unifiedAIService } from '../UnifiedAIService';

// Mock the unified AI service
jest.mock('../UnifiedAIService', () => ({
  unifiedAIService: {
    askQuestion: jest.fn(),
    getAnalytics: jest.fn(),
    sendMessage: jest.fn(),
    getConversations: jest.fn(),
    getConversation: jest.fn(),
    deleteConversation: jest.fn(),
    getStudySessions: jest.fn(),
    startStudySession: jest.fn(),
    endStudySession: jest.fn(),
    generateStudyPlan: jest.fn(),
    checkAnswer: jest.fn(),
  },
}));

// Mock console methods to test deprecation warnings
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleGroup = jest.spyOn(console, 'group').mockImplementation();
const mockConsoleInfo = jest.spyOn(console, 'info').mockImplementation();
const mockConsoleGroupEnd = jest.spyOn(console, 'groupEnd').mockImplementation();

describe('Service Migration Adapters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('AIAssistantServiceAdapter', () => {
    describe('askQuestion', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockResponse = {
          answer: 'Test answer',
          confidence_score: 0.9,
          response_time: 1000,
          session_id: 'test-session',
        };

        (unifiedAIService.askQuestion as jest.Mock).mockResolvedValue(mockResponse);

        const result = await aiAssistantServiceAdapter.askQuestion('Test question', {
          subject: 'math',
          level: 'beginner',
        });

        expect(unifiedAIService.askQuestion).toHaveBeenCalledWith('Test question', {
          subject: 'math',
          level: 'beginner',
        });

        expect(result).toEqual(mockResponse);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: aiAssistantService.askQuestion()')
        );
      });
    });

    describe('getSuggestions', () => {
      it('should get suggestions from analytics and show deprecation warning', async () => {
        const mockAnalytics = {
          data: {
            suggestions: ['suggestion1', 'suggestion2'],
          },
        };

        (unifiedAIService.getAnalytics as jest.Mock).mockResolvedValue(mockAnalytics);

        const result = await aiAssistantServiceAdapter.getSuggestions();

        expect(unifiedAIService.getAnalytics).toHaveBeenCalled();
        expect(result).toEqual(['suggestion1', 'suggestion2']);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: aiAssistantService.getSuggestions()')
        );
      });

      it('should return empty array on error', async () => {
        (unifiedAIService.getAnalytics as jest.Mock).mockRejectedValue(new Error('Test error'));

        const result = await aiAssistantServiceAdapter.getSuggestions();

        expect(result).toEqual([]);
      });
    });

    describe('getAnalytics', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockAnalytics = {
          data: {
            metrics: { accuracy: 0.95 },
          },
        };

        (unifiedAIService.getAnalytics as jest.Mock).mockResolvedValue(mockAnalytics);

        const result = await aiAssistantServiceAdapter.getAnalytics();

        expect(unifiedAIService.getAnalytics).toHaveBeenCalled();
        expect(result).toEqual(mockAnalytics.data);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: aiAssistantService.getAnalytics()')
        );
      });
    });
  });

  describe('ChatbotServiceAdapter', () => {
    describe('sendMessage', () => {
      it('should delegate to unified service and return legacy format', async () => {
        const mockResponse = {
          data: {
            content: 'Test response',
            timestamp: '2024-01-01T00:00:00Z',
          },
          metadata: {
            processingTime: 1000,
          },
        };

        (unifiedAIService.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

        const result = await chatbotServiceAdapter.sendMessage('Hello', 123);

        expect(unifiedAIService.sendMessage).toHaveBeenCalledWith('Hello', 123, { type: 'general' });

        expect(result).toEqual({
          message: 'Test response',
          conversation_id: 123,
          timestamp: '2024-01-01T00:00:00Z',
          metadata: { processingTime: 1000 },
        });

        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: chatbotService.sendMessage()')
        );
      });
    });

    describe('getConversations', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockConversations = {
          data: [
            { id: 1, title: 'Test Conversation' },
          ],
        };

        (unifiedAIService.getConversations as jest.Mock).mockResolvedValue(mockConversations);

        const result = await chatbotServiceAdapter.getConversations();

        expect(unifiedAIService.getConversations).toHaveBeenCalled();
        expect(result).toEqual(mockConversations.data);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: chatbotService.getConversations()')
        );
      });
    });

    describe('deleteConversation', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        (unifiedAIService.deleteConversation as jest.Mock).mockResolvedValue(undefined);

        await chatbotServiceAdapter.deleteConversation(123);

        expect(unifiedAIService.deleteConversation).toHaveBeenCalledWith(123);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: chatbotService.deleteConversation()')
        );
      });
    });

    describe('deprecated methods', () => {
      it('should throw error for updateConversationTitle', async () => {
        await expect(chatbotServiceAdapter.updateConversationTitle(1, 'New Title'))
          .rejects.toThrow('updateConversationTitle is deprecated');

        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: chatbotService.updateConversationTitle()')
        );
      });

      it('should throw error for getMessages', async () => {
        await expect(chatbotServiceAdapter.getMessages(1))
          .rejects.toThrow('getMessages is deprecated');

        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: chatbotService.getMessages()')
        );
      });
    });
  });

  describe('StudyAssistantServiceAdapter', () => {
    describe('getStudySessions', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockSessions = {
          data: [
            { id: 1, course_id: 1, topics_covered: ['math'] },
          ],
        };

        (unifiedAIService.getStudySessions as jest.Mock).mockResolvedValue(mockSessions);

        const result = await studyAssistantServiceAdapter.getStudySessions();

        expect(unifiedAIService.getStudySessions).toHaveBeenCalled();
        expect(result).toEqual(mockSessions.data);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: studyAssistantService.getStudySessions()')
        );
      });
    });

    describe('startStudySession', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockSession = {
          data: {
            id: 1,
            course_id: 1,
            topics_covered: ['algebra'],
          },
        };

        (unifiedAIService.startStudySession as jest.Mock).mockResolvedValue(mockSession);

        const result = await studyAssistantServiceAdapter.startStudySession(1, ['algebra']);

        expect(unifiedAIService.startStudySession).toHaveBeenCalledWith(1, ['algebra']);
        expect(result).toEqual(mockSession.data);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: studyAssistantService.startStudySession()')
        );
      });
    });

    describe('generateStudyPlan', () => {
      it('should delegate to unified service and show deprecation warning', async () => {
        const mockPlan = {
          data: {
            id: 1,
            course_id: 1,
            total_duration: '8 weeks',
          },
        };

        (unifiedAIService.generateStudyPlan as jest.Mock).mockResolvedValue(mockPlan);

        const result = await studyAssistantServiceAdapter.generateStudyPlan(1);

        expect(unifiedAIService.generateStudyPlan).toHaveBeenCalledWith(1);
        expect(result).toEqual(mockPlan.data);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: studyAssistantService.generateStudyPlan()')
        );
      });
    });

    describe('getRecommendations', () => {
      it('should get recommendations from analytics', async () => {
        const mockAnalytics = {
          data: {
            recommendations: ['recommendation1', 'recommendation2'],
          },
        };

        (unifiedAIService.getAnalytics as jest.Mock).mockResolvedValue(mockAnalytics);

        const result = await studyAssistantServiceAdapter.getRecommendations();

        expect(unifiedAIService.getAnalytics).toHaveBeenCalled();
        expect(result).toEqual(['recommendation1', 'recommendation2']);
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          expect.stringContaining('DEPRECATED: studyAssistantService.getRecommendations()')
        );
      });

      it('should return empty array on error', async () => {
        (unifiedAIService.getAnalytics as jest.Mock).mockRejectedValue(new Error('Test error'));

        const result = await studyAssistantServiceAdapter.getRecommendations();

        expect(result).toEqual([]);
      });
    });
  });

  describe('ServiceMigrationHelper', () => {
    describe('logMigrationWarning', () => {
      it('should log structured migration warning', () => {
        ServiceMigrationHelper.logMigrationWarning(
          'aiAssistantService',
          'askQuestion',
          'askTutoringQuestion'
        );

        expect(mockConsoleGroup).toHaveBeenCalledWith('🔄 Service Migration Warning');
        expect(mockConsoleWarn).toHaveBeenCalledWith(
          'The aiAssistantService.askQuestion() method is deprecated.'
        );
        expect(mockConsoleInfo).toHaveBeenCalledWith(
          'Please migrate to: unifiedAIService.askTutoringQuestion()'
        );
        expect(mockConsoleInfo).toHaveBeenCalledWith(
          'See migration guide: /docs/service-migration.md'
        );
        expect(mockConsoleGroupEnd).toHaveBeenCalled();
      });
    });

    describe('checkMigrationStatus', () => {
      it('should return migration status', () => {
        const status = ServiceMigrationHelper.checkMigrationStatus();

        expect(status).toHaveProperty('migrated');
        expect(status).toHaveProperty('warnings');
        expect(Array.isArray(status.warnings)).toBe(true);
      });
    });

    describe('getMigrationRecommendations', () => {
      it('should return migration recommendations', () => {
        const recommendations = ServiceMigrationHelper.getMigrationRecommendations();

        expect(Array.isArray(recommendations)).toBe(true);
        expect(recommendations.length).toBeGreaterThan(0);
        expect(recommendations[0]).toContain('unifiedAIService');
      });
    });
  });
});
