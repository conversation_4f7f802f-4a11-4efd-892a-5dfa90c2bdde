import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { alpha } from '@mui/material/styles';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  Button,
  Stack,
  LinearProgress,
  Alert,
  useTheme,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Dialog,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  AreaChart,
  Area,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import ModernHeroSVG from '../home/<USER>';
import ProfessorAppBar from './components/ProfessorAppBar';
import PeopleIcon from '../../components/icons/People';
import AssignmentIcon from '../../components/icons/Assignment';
import EventIcon from '../../components/icons/Event';
import SpeedIcon from '../../components/icons/Speed';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import BarChartIcon from '@mui/icons-material/BarChart';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import SchoolIcon from '@mui/icons-material/School';
import GradeIcon from '@mui/icons-material/Grade';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import NotificationsIcon from '@mui/icons-material/Notifications';
import MoreVertIcon from '@mui/icons-material/MoreVert';

// Removed unused course components
import {
  Timeline,
  TimelineItem,
  TimelineContent,
  TimelineSeparator,
  TimelineConnector,
  TimelineDot,
} from '@mui/lab';
import { courseService } from '../../services/courseService';
import { gradeService } from '../../services/gradeService';
import { attendanceService } from '../../services/attendanceService';
import { notificationService } from '../../services/notificationService';
import { OfficeHoursManagement } from './components/OfficeHoursManagement';
import { OfficeHoursWidget } from './components/OfficeHoursWidget';
import { CourseAnnouncements } from './components/CourseAnnouncements';
import './styles/ProfessorDashboard.css';

interface DashboardStats {
  totalStudents: number;
  activeAssignments: number;
  upcomingClasses: number;
  averageAttendance: number;
}

interface Course {
  id: number;
  code?: string;
  course_code?: string;
  title: string;
  description?: string;
  credits?: number;
  students_count?: number;
  capacity?: number;
  instructor?: string;
  department?: string;
  semester?: string;
  academic_year?: string;
  progress?: number;
  next_class?: string;
  last_attendance_date?: string;
  last_grade_date?: string;
  last_material_date?: string;
}

interface Activity {
  id: number;
  type: string;
  text: string;
  time: string;
}

interface StatsCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

interface ChartData {
  name: string;
  value: number;
  color?: string;
}

interface AttendanceData {
  date: string;
  attendance: number;
  target: number;
}

interface GradeDistribution {
  grade: string;
  count: number;
  percentage: number;
}

const ProfessorDashboardHome: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation(['translation', 'professor']);
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalStudents: 0,
    activeAssignments: 0,
    upcomingClasses: 0,
    averageAttendance: 0,
  });
  const [courses, setCourses] = useState<Course[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [gradeDistribution, setGradeDistribution] = useState<GradeDistribution[]>([]);
  const [coursePerformance, setCoursePerformance] = useState<ChartData[]>([]);
  const [showOfficeHoursManagement, setShowOfficeHoursManagement] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all data in parallel
      const [
        coursesResponse,
        assignmentsResponse,
        attendanceResponse,
        notificationsResponse,
      ] = await Promise.all([
        courseService.getProfessorCourses(),
        gradeService.getCourseGrades(),
        attendanceService.getAttendanceStats(0),
        notificationService.getNotifications('professor'),
      ]);

      // Process courses data - ensure we have an array
      const coursesData = coursesResponse.data?.data || [];
      setCourses(coursesData);

      // Calculate total students
      const totalStudents = coursesData.reduce(
        (sum: number, course: Course) => sum + (course.students_count || 0),
        0
      );

      // Process assignments data
      const activeAssignments = assignmentsResponse.data?.active_count || 0;

      // Process attendance data
      const attendanceStats = attendanceResponse.data?.overall || {};
      const {
        total_students = 0,
        total_courses = 0,
        average_attendance_rate = 0,
      } = attendanceStats;

      // Update stats with actual data
      setStats({
        totalStudents: total_students || totalStudents,
        activeAssignments,
        upcomingClasses: total_courses || coursesData.length,
        averageAttendance: Math.round(average_attendance_rate),
      });

      // Process notifications into activities - directly use the response array
      const recentActivities = (
        Array.isArray(notificationsResponse.data)
          ? notificationsResponse.data
          : []
      )
        .filter(notification =>
          ['ATTENDANCE', 'GRADE', 'MATERIAL'].includes(notification.type)
        )
        .map(notification => ({
          id: notification.id,
          type: notification.type.toLowerCase(),
          text: notification.message,
          time: notification.created_at,
        }))
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
        .slice(0, 5);

      setActivities(recentActivities);

      // Generate mock chart data
      generateChartData();
    } catch (err: any) {
      console.error('Dashboard data fetch error:', err);
      setError(err.response?.data?.message || t('dashboard.fetchError', { ns: 'professor' }));
    } finally {
      setLoading(false);
    }
  };

  const generateChartData = () => {
    // Mock attendance data for the last 7 days
    const attendanceData: AttendanceData[] = [
      { date: t('days.mon', { ns: 'professor' }), attendance: 85, target: 90 },
      { date: t('days.tue', { ns: 'professor' }), attendance: 92, target: 90 },
      { date: t('days.wed', { ns: 'professor' }), attendance: 78, target: 90 },
      { date: t('days.thu', { ns: 'professor' }), attendance: 88, target: 90 },
      { date: t('days.fri', { ns: 'professor' }), attendance: 95, target: 90 },
      { date: t('days.sat', { ns: 'professor' }), attendance: 82, target: 90 },
      { date: t('days.sun', { ns: 'professor' }), attendance: 90, target: 90 },
    ];
    setAttendanceData(attendanceData);

    // Mock grade distribution
    const gradeDistribution: GradeDistribution[] = [
      { grade: t('grades.a', { ns: 'professor' }), count: 25, percentage: 35 },
      { grade: t('grades.b', { ns: 'professor' }), count: 20, percentage: 28 },
      { grade: t('grades.c', { ns: 'professor' }), count: 15, percentage: 21 },
      { grade: t('grades.d', { ns: 'professor' }), count: 8, percentage: 11 },
      { grade: t('grades.f', { ns: 'professor' }), count: 4, percentage: 5 },
    ];
    setGradeDistribution(gradeDistribution);

    // Mock course performance
    const coursePerformance: ChartData[] = [
      { name: t('courses.mathematics', { ns: 'professor' }), value: 92, color: '#1976d2' },
      { name: t('courses.physics', { ns: 'professor' }), value: 88, color: '#2e7d32' },
      { name: t('courses.chemistry', { ns: 'professor' }), value: 85, color: '#ed6c02' },
      { name: t('courses.biology', { ns: 'professor' }), value: 90, color: '#9c27b0' },
    ];
    setCoursePerformance(coursePerformance);
  };

  // Removed unused formatTimeAgo function

  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center' }}>
        <LinearProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='error'>{error}</Alert>
      </Box>
    );
  }

  // Removed unused toggleDarkMode function

  return (
    <Box
      className={`professor-dashboard ${theme.palette.mode === 'dark' ? 'dark-mode' : ''}`}
      sx={{
        minHeight: '100vh',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, rgba(18,18,18,0.95) 0%, rgba(0,229,255,0.08) 100%)'
          : 'linear-gradient(135deg, rgba(248,250,252,0.95) 0%, rgba(0,119,182,0.03) 100%)',
        position: 'relative',
        overflow: 'hidden',
        pb: { xs: 2, sm: 3 },
      }}
    >
      {/* Hero Background SVG */}
      <ModernHeroSVG />
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{ position: 'relative', zIndex: 1 }}
      >
        <ProfessorAppBar
          darkMode={theme.palette.mode === 'dark'}
          toggleDarkMode={() => {}} // Placeholder function
          refreshData={fetchDashboardData}
        />

        <Box sx={{
          px: { xs: 2, sm: 3 }, // Responsive padding
          maxWidth: '1400px', // Max width for large screens
          mx: 'auto' // Center content
        }}>

        {/* Enhanced Header Section */}
        <Box sx={{ mb: 4 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    width: 60,
                    height: 60,
                    mr: 3,
                    background: 'linear-gradient(135deg, #1976d2, #42a5f5)',
                    fontSize: '1.5rem',
                    fontWeight: 'bold',
                  }}
                >
                  P
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}>
                    {t('dashboard.welcome', { ns: 'professor' })} 👋
                  </Typography>
                  <Typography variant="body1" color="textSecondary">
                    {t('dashboard.subtitle', {
                      ns: 'professor',
                      courses: courses.length,
                      students: stats.totalStudents,
                      assignments: stats.activeAssignments
                    })}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Stack direction="row" spacing={2} justifyContent={{ xs: 'flex-start', md: 'flex-end' }}>
                <Button
                  variant="contained"
                  startIcon={<CalendarTodayIcon />}
                  onClick={() => navigate('/professor/schedule')}
                  sx={{
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 'bold',
                    background: 'linear-gradient(135deg, #1976d2, #42a5f5)',
                    '&:hover': {
                      background: 'linear-gradient(135deg, #1565c0, #1976d2)',
                    },
                  }}
                >
                  {t('dashboard.scheduleClass', { ns: 'professor' })}
                </Button>
                <Tooltip title={t('common.notifications', { ns: 'professor' })}>
                  <IconButton
                    onClick={() => navigate('/professor/notifications')}
                    sx={{
                      backgroundColor: alpha('#1976d2', 0.1),
                      '&:hover': {
                        backgroundColor: alpha('#1976d2', 0.2),
                      },
                    }}
                  >
                    <NotificationsIcon color="primary" />
                  </IconButton>
                </Tooltip>
                <Tooltip title={t('common.moreOptions', { ns: 'professor' })}>
                  <IconButton
                    sx={{
                      backgroundColor: alpha('#1976d2', 0.1),
                      '&:hover': {
                        backgroundColor: alpha('#1976d2', 0.2),
                      },
                    }}
                  >
                    <MoreVertIcon color="primary" />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Grid>
          </Grid>
        </Box>
        {/* Stats Cards */}
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<PeopleIcon />}
              title={t('dashboard.totalStudents', { ns: 'professor' })}
              value={stats.totalStudents}
              color='#1976d2'
              trend={{ value: 12, isPositive: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<AssignmentIcon />}
              title={t('dashboard.activeAssignments', { ns: 'professor' })}
              value={stats.activeAssignments}
              color='#2e7d32'
              trend={{ value: 8, isPositive: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<EventIcon />}
              title={t('dashboard.upcomingClasses', { ns: 'professor' })}
              value={stats.upcomingClasses}
              color='#ed6c02'
              trend={{ value: 3, isPositive: false }}
            />
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <StatsCard
              icon={<SpeedIcon />}
              title={t('dashboard.avgAttendance', { ns: 'professor' })}
              value={`${stats.averageAttendance}%`}
              color='#9c27b0'
              trend={{ value: 5, isPositive: true }}
            />
          </Grid>
        </Grid>

        {/* Charts Section */}
        <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ mb: 4 }}>
          {/* Attendance Trend Chart */}
          <Grid item xs={12} lg={6}>
            <AttendanceTrendChart data={attendanceData} t={t} />
          </Grid>

          {/* Grade Distribution Chart */}
          <Grid item xs={12} lg={6}>
            <GradeDistributionChart data={gradeDistribution} t={t} />
          </Grid>

          {/* Course Performance Chart */}
          <Grid item xs={12} lg={6}>
            <CoursePerformanceChart data={coursePerformance} t={t} />
          </Grid>

          {/* Quick Stats Chart */}
          <Grid item xs={12} lg={6}>
            <QuickStatsChart t={t} />
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={{ xs: 2, sm: 3 }}>
          {/* Left Column - Main Content */}
          <Grid item xs={12} lg={8}>
            <Stack spacing={3}>
              {/* Course Announcements */}
              <CourseAnnouncements />

              {/* Recent Activity Timeline */}
              <RecentActivityCard activities={activities} t={t} />

            </Stack>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={4}>
            <Stack spacing={3}>
              {/* Office Hours Widget */}
              <OfficeHoursWidget
                onManageClick={() => setShowOfficeHoursManagement(true)}
              />

              {/* Quick Actions Widget */}
              <QuickActionsWidget t={t} />

              {/* Course Overview Cards */}
              <Card
                sx={{
                  borderRadius: 3,
                  background: alpha(theme.palette.background.paper, 0.9),
                  backdropFilter: 'blur(20px)',
                  border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        mr: 2,
                        background: 'linear-gradient(135deg, #2e7d32, #4caf50)',
                      }}
                    >
                      <SchoolIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="bold">
                        {t('dashboard.myCourses', { ns: 'professor' })}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('dashboard.activeCourses', { ns: 'professor', count: courses.length })}
                      </Typography>
                    </Box>
                  </Box>
                  <Stack spacing={2}>
                    {courses.slice(0, 3).map(course => (
                      <Card
                        key={course.id}
                        sx={{
                          borderRadius: 2,
                          background: alpha('#2e7d32', 0.05),
                          border: `1px solid ${alpha('#2e7d32', 0.2)}`,
                          transition: 'all 0.3s ease',
                          cursor: 'pointer',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: `0 4px 16px ${alpha('#2e7d32', 0.3)}`,
                          },
                        }}
                        onClick={() => navigate(`/professor/courses/${course.id}`)}
                      >
                        <CardContent sx={{ p: 2 }}>
                          <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                            {course.course_code}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                            {course.title}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Chip
                              label={t('dashboard.students', { ns: 'professor', count: course.students_count || 0 })}
                              size="small"
                              sx={{
                                backgroundColor: '#2e7d32',
                                color: 'white',
                                fontSize: '0.7rem',
                              }}
                            />
                            <Typography variant="caption" color="textSecondary">
                              {course.semester}
                            </Typography>
                          </Box>
                        </CardContent>
                      </Card>
                    ))}
                    {courses.length > 3 && (
                      <Button
                        variant="text"
                        size="small"
                        sx={{ mt: 1 }}
                        onClick={() => navigate('/professor/courses')}
                      >
                        {t('dashboard.viewAllCourses', { ns: 'professor', count: courses.length })}
                      </Button>
                    )}
                  </Stack>
                </CardContent>
              </Card>
            </Stack>
          </Grid>
        </Grid>
        </Box>

        {/* Office Hours Management Dialog */}
        <Dialog
          open={showOfficeHoursManagement}
          onClose={() => setShowOfficeHoursManagement(false)}
          maxWidth="lg"
          fullWidth
          PaperProps={{
            sx: {
              borderRadius: 3,
              background: alpha(theme.palette.background.paper, 0.95),
              backdropFilter: 'blur(20px)',
            },
          }}
        >
          <DialogContent sx={{ p: 0 }}>
            <OfficeHoursManagement />
          </DialogContent>
          <DialogActions sx={{ p: 3 }}>
            <Button
              onClick={() => setShowOfficeHoursManagement(false)}
              variant="outlined"
              sx={{ borderRadius: 2 }}
            >
              {t('common.close', { ns: 'professor' })}
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  );
};

// Helper Components
const StatsCard = ({ icon, title, value, color, trend }: StatsCardProps) => {
  const theme = useTheme();

  return (
    <Card
      className="stats-card"
      sx={{
        height: '100%',
        minHeight: { xs: '140px', sm: '160px' }, // Increased height for trend
        borderRadius: { xs: 2, sm: 3 },
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
        boxShadow: theme.palette.mode === 'dark'
          ? '0 8px 32px rgba(0, 0, 0, 0.3)'
          : '0 8px 32px rgba(0, 0, 0, 0.08)',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-6px)',
          boxShadow: theme.palette.mode === 'dark'
            ? '0 16px 48px rgba(0, 0, 0, 0.4)'
            : '0 16px 48px rgba(0, 0, 0, 0.12)',
          '& .stats-icon': {
            transform: 'scale(1.1) rotate(5deg)',
          },
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, ${color}, ${color}80)`,
          borderRadius: '3px 3px 0 0',
        },
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box
            className="stats-icon"
            sx={{
              p: { xs: 1, sm: 1.5 },
              borderRadius: 2,
              background: `linear-gradient(135deg, ${color}20, ${color}30)`,
              color: color,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease',
              fontSize: { xs: '1.2rem', sm: '1.5rem' },
            }}
          >
            {icon}
          </Box>
          {trend && (
            <Chip
              icon={trend.isPositive ? <TrendingUpIcon /> : <TrendingDownIcon />}
              label={`${trend.isPositive ? '+' : '-'}${trend.value}%`}
              size="small"
              sx={{
                backgroundColor: trend.isPositive
                  ? alpha('#4caf50', 0.1)
                  : alpha('#f44336', 0.1),
                color: trend.isPositive ? '#4caf50' : '#f44336',
                fontWeight: 'bold',
                '& .MuiChip-icon': {
                  color: 'inherit',
                },
              }}
            />
          )}
        </Box>
        <Typography
          variant='h4'
          fontWeight='bold'
          sx={{
            fontSize: { xs: '1.5rem', sm: '2rem' },
            mb: 1,
            background: theme.palette.mode === 'dark'
              ? `linear-gradient(135deg, ${color} 0%, #ffffff 100%)`
              : `linear-gradient(135deg, ${color} 0%, #333333 100%)`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
          }}
        >
          {value}
        </Typography>
        <Typography
          variant='body2'
          color='textSecondary'
          sx={{
            fontWeight: 500,
            fontSize: { xs: '0.75rem', sm: '0.875rem' },
            lineHeight: 1.4,
          }}
        >
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};

// Using the shared CourseCard component instead

// Chart Components
const AttendanceTrendChart = ({ data, t }: { data: AttendanceData[]; t: any }) => {
  const theme = useTheme();

  return (
    <Card
      className="chart-container"
      sx={{
        height: '400px',
        borderRadius: 3,
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
      }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <TimelineIcon sx={{ mr: 1, color: '#1976d2' }} />
          <Typography variant="h6" fontWeight="bold">
            {t('dashboard.attendanceTrend', { ns: 'professor' })}
          </Typography>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data}>
            <defs>
              <linearGradient id="attendanceGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#1976d2" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#1976d2" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
            <XAxis
              dataKey="date"
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                borderRadius: '8px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              }}
            />
            <Area
              type="monotone"
              dataKey="attendance"
              stroke="#1976d2"
              strokeWidth={3}
              fill="url(#attendanceGradient)"
            />
            <Line
              type="monotone"
              dataKey="target"
              stroke="#ff9800"
              strokeWidth={2}
              strokeDasharray="5 5"
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const GradeDistributionChart = ({ data, t }: { data: GradeDistribution[]; t: any }) => {
  const theme = useTheme();
  const COLORS = ['#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0'];

  return (
    <Card sx={{
      height: '400px',
      borderRadius: 3,
      background: alpha(theme.palette.background.paper, 0.9),
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <PieChartIcon sx={{ mr: 1, color: '#2e7d32' }} />
          <Typography variant="h6" fontWeight="bold">
            {t('dashboard.gradeDistribution', { ns: 'professor' })}
          </Typography>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={5}
              dataKey="count"
            >
              {data.map((_, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <RechartsTooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                borderRadius: '8px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const CoursePerformanceChart = ({ data, t }: { data: ChartData[]; t: any }) => {
  const theme = useTheme();

  return (
    <Card sx={{
      height: '400px',
      borderRadius: 3,
      background: alpha(theme.palette.background.paper, 0.9),
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <BarChartIcon sx={{ mr: 1, color: '#ed6c02' }} />
          <Typography variant="h6" fontWeight="bold">
            {t('dashboard.coursePerformance', { ns: 'professor' })}
          </Typography>
        </Box>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
            <XAxis
              dataKey="name"
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <RechartsTooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                borderRadius: '8px',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              }}
            />
            <Bar
              dataKey="value"
              radius={[8, 8, 0, 0]}
              fill="#ed6c02"
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

const QuickStatsChart = ({ t }: { t: any }) => {
  const theme = useTheme();
  const quickStats = [
    { name: t('dashboard.completed', { ns: 'professor' }), value: 75, color: '#4caf50' },
    { name: t('dashboard.inProgress', { ns: 'professor' }), value: 20, color: '#ff9800' },
    { name: t('dashboard.pending', { ns: 'professor' }), value: 5, color: '#f44336' },
  ];

  return (
    <Card sx={{
      height: '400px',
      borderRadius: 3,
      background: alpha(theme.palette.background.paper, 0.9),
      backdropFilter: 'blur(20px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <GradeIcon sx={{ mr: 1, color: '#9c27b0' }} />
          <Typography variant="h6" fontWeight="bold">
            {t('dashboard.assignmentStatus', { ns: 'professor' })}
          </Typography>
        </Box>
        <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', gap: 3 }}>
          {quickStats.map((stat, index) => (
            <Box key={index}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" fontWeight="medium">
                  {stat.name}
                </Typography>
                <Typography variant="body2" fontWeight="bold" color={stat.color}>
                  {stat.value}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={stat.value}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: alpha(stat.color, 0.1),
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: stat.color,
                    borderRadius: 4,
                  },
                }}
              />
            </Box>
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

// Enhanced Component Functions
const RecentActivityCard = ({ activities, t }: { activities: Activity[]; t: any }) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        borderRadius: 3,
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              mr: 2,
              background: 'linear-gradient(135deg, #9c27b0, #e1bee7)',
            }}
          >
            <TimelineIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              {t('dashboard.recentActivity', { ns: 'professor' })}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {t('dashboard.latestUpdates', { ns: 'professor' })}
            </Typography>
          </Box>
        </Box>

        {activities.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
            <TimelineIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="body2">{t('dashboard.noRecentActivity', { ns: 'professor' })}</Typography>
          </Box>
        ) : (
          <Timeline>
            {activities.slice(0, 5).map((activity, index) => (
              <TimelineItem key={activity.id || index}>
                <TimelineSeparator>
                  <TimelineDot sx={{ backgroundColor: '#9c27b0' }} />
                  {index < activities.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Typography variant="subtitle2" fontWeight="medium">
                    {activity.text}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {activity.time}
                  </Typography>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        )}
      </CardContent>
    </Card>
  );
};

const QuickActionsWidget = ({ t }: { t: any }) => {
  const theme = useTheme();
  const navigate = useNavigate();

  const quickActions = [
    {
      title: t('dashboard.createAssignment', { ns: 'professor' }),
      icon: <AssignmentIcon />,
      color: '#1976d2',
      action: () => navigate('/professor/assignments/create'),
    },
    {
      title: t('dashboard.takeAttendance', { ns: 'professor' }),
      icon: <PeopleIcon />,
      color: '#2e7d32',
      action: () => navigate('/professor/attendance'),
    },
    {
      title: t('dashboard.gradeSubmissions', { ns: 'professor' }),
      icon: <GradeIcon />,
      color: '#ed6c02',
      action: () => navigate('/professor/grading'),
    },
    {
      title: t('dashboard.scheduleClass', { ns: 'professor' }),
      icon: <CalendarTodayIcon />,
      color: '#9c27b0',
      action: () => navigate('/professor/schedule'),
    },
  ];

  return (
    <Card
      sx={{
        borderRadius: 3,
        background: alpha(theme.palette.background.paper, 0.9),
        backdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              mr: 2,
              background: 'linear-gradient(135deg, #f44336, #ffcdd2)',
            }}
          >
            <TrendingUpIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              {t('dashboard.quickActions', { ns: 'professor' })}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {t('dashboard.commonTasks', { ns: 'professor' })}
            </Typography>
          </Box>
        </Box>

        <Stack spacing={2}>
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant="outlined"
              startIcon={action.icon}
              onClick={action.action}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                justifyContent: 'flex-start',
                borderColor: alpha(action.color, 0.3),
                color: action.color,
                '&:hover': {
                  backgroundColor: alpha(action.color, 0.1),
                  borderColor: action.color,
                },
              }}
            >
              {action.title}
            </Button>
          ))}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default ProfessorDashboardHome;
