#!/usr/bin/env node

/**
 * Hardcoded String Audit Script
 * 
 * This script scans the React codebase for hardcoded strings that should be
 * replaced with translation keys for proper internationalization.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const CONFIG = {
  // Directories to scan
  scanDirs: [
    'src/components/**/*.{ts,tsx}',
    'src/features/**/*.{ts,tsx}',
    'src/pages/**/*.{ts,tsx}',
    'src/services/**/*.{ts,tsx}',
  ],
  
  // Files to exclude
  excludePatterns: [
    '**/node_modules/**',
    '**/__tests__/**',
    '**/__mocks__/**',
    '**/test-*',
    '**/*.test.*',
    '**/*.spec.*',
  ],
  
  // Patterns to detect hardcoded strings
  patterns: [
    // JSX text content
    {
      name: 'JSX Text Content',
      regex: />([^<>{}\n]+[a-zA-Z][^<>{}\n]*)</g,
      description: 'Text content directly in JSX elements',
    },
    
    // String literals in props
    {
      name: 'String Props',
      regex: /(\w+)=["']([^"'{}]+)["']/g,
      description: 'String literals passed as props',
      exclude: ['className', 'style', 'id', 'data-', 'aria-', 'role', 'type', 'name', 'value', 'placeholder'],
    },
    
    // Object property strings
    {
      name: 'Object Properties',
      regex: /(\w+):\s*["']([^"'{}]+)["']/g,
      description: 'String values in object properties',
      exclude: ['key', 'id', 'className', 'style', 'type'],
    },
    
    // Array string literals
    {
      name: 'Array Strings',
      regex: /\[([^[\]]*["']([^"']+)["'][^[\]]*)\]/g,
      description: 'String literals in arrays',
    },
    
    // Function call arguments
    {
      name: 'Function Arguments',
      regex: /\w+\(["']([^"'{}]+)["']\)/g,
      description: 'String literals as function arguments',
      exclude: ['console.log', 'console.error', 'console.warn', 'console.info'],
    },
  ],
  
  // Common words that indicate user-facing text
  userFacingIndicators: [
    'error', 'success', 'warning', 'info', 'message', 'title', 'description',
    'label', 'placeholder', 'button', 'link', 'text', 'content', 'name',
    'welcome', 'hello', 'goodbye', 'thank', 'please', 'sorry', 'loading',
    'save', 'delete', 'edit', 'create', 'update', 'cancel', 'confirm',
    'yes', 'no', 'ok', 'close', 'open', 'submit', 'send', 'receive',
  ],
  
  // Minimum string length to consider
  minStringLength: 3,
  
  // Maximum string length to avoid very long strings
  maxStringLength: 100,
};

// Results storage
const results = {
  files: [],
  totalIssues: 0,
  categories: {},
};

/**
 * Check if a string should be excluded from audit
 */
function shouldExcludeString(str, context = {}) {
  // Exclude very short or very long strings
  if (str.length < CONFIG.minStringLength || str.length > CONFIG.maxStringLength) {
    return true;
  }
  
  // Exclude strings that are clearly technical (URLs, IDs, etc.)
  if (/^(https?:\/\/|\/|#|\.|\w+\.\w+|[A-Z_]+|[a-f0-9-]{8,})/.test(str)) {
    return true;
  }
  
  // Exclude strings that are only numbers, symbols, or single words without spaces
  if (/^[\d\s\-_.,!@#$%^&*()+=[\]{}|\\:";'<>?/~`]+$/.test(str)) {
    return true;
  }
  
  // Exclude CSS classes, HTML attributes, etc.
  if (context.propName && CONFIG.patterns[1].exclude.some(ex => context.propName.startsWith(ex))) {
    return true;
  }
  
  return false;
}

/**
 * Check if a string is likely user-facing
 */
function isLikelyUserFacing(str) {
  const lowerStr = str.toLowerCase();
  
  // Check for user-facing indicators
  const hasUserFacingWords = CONFIG.userFacingIndicators.some(indicator => 
    lowerStr.includes(indicator)
  );
  
  // Check for sentence-like structure (contains spaces and common words)
  const hasSentenceStructure = /\s/.test(str) && /\b(the|a|an|is|are|was|were|have|has|will|would|can|could|should|may|might)\b/i.test(str);
  
  // Check for common UI text patterns
  const hasUIPatterns = /\b(click|select|choose|enter|type|upload|download|view|show|hide|toggle)\b/i.test(str);
  
  return hasUserFacingWords || hasSentenceStructure || hasUIPatterns;
}

/**
 * Scan a file for hardcoded strings
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileResults = {
      path: filePath,
      issues: [],
    };
    
    // Skip files that already use translation extensively
    const translationUsage = (content.match(/\bt\(/g) || []).length;
    const useTranslationUsage = (content.match(/useTranslation/g) || []).length;
    
    if (translationUsage > 5 || useTranslationUsage > 0) {
      // File already uses translations, but still check for missed strings
    }
    
    CONFIG.patterns.forEach(pattern => {
      let match;
      const regex = new RegExp(pattern.regex.source, pattern.regex.flags);
      
      while ((match = regex.exec(content)) !== null) {
        const fullMatch = match[0];
        const stringValue = match[2] || match[1];
        
        if (!stringValue || shouldExcludeString(stringValue, { propName: match[1] })) {
          continue;
        }
        
        // Calculate line number
        const beforeMatch = content.substring(0, match.index);
        const lineNumber = beforeMatch.split('\n').length;
        
        // Get surrounding context
        const lines = content.split('\n');
        const contextStart = Math.max(0, lineNumber - 2);
        const contextEnd = Math.min(lines.length, lineNumber + 1);
        const context = lines.slice(contextStart, contextEnd).join('\n');
        
        const issue = {
          type: pattern.name,
          string: stringValue,
          fullMatch,
          lineNumber,
          context,
          isLikelyUserFacing: isLikelyUserFacing(stringValue),
          severity: isLikelyUserFacing(stringValue) ? 'high' : 'medium',
        };
        
        fileResults.issues.push(issue);
      }
    });
    
    if (fileResults.issues.length > 0) {
      results.files.push(fileResults);
      results.totalIssues += fileResults.issues.length;
      
      // Categorize issues
      fileResults.issues.forEach(issue => {
        if (!results.categories[issue.type]) {
          results.categories[issue.type] = 0;
        }
        results.categories[issue.type]++;
      });
    }
    
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Generate audit report
 */
function generateReport() {
  console.log('\n🔍 Hardcoded String Audit Report');
  console.log('='.repeat(50));
  
  console.log(`\n📊 Summary:`);
  console.log(`  Total files scanned: ${results.files.length}`);
  console.log(`  Total issues found: ${results.totalIssues}`);
  
  console.log(`\n📈 Issues by category:`);
  Object.entries(results.categories).forEach(([category, count]) => {
    console.log(`  ${category}: ${count}`);
  });
  
  console.log(`\n🔥 High Priority Issues (User-facing strings):`);
  let highPriorityCount = 0;
  
  results.files.forEach(file => {
    const highPriorityIssues = file.issues.filter(issue => issue.severity === 'high');
    if (highPriorityIssues.length > 0) {
      console.log(`\n  📁 ${file.path}`);
      highPriorityIssues.slice(0, 5).forEach(issue => { // Show first 5 issues per file
        console.log(`    Line ${issue.lineNumber}: "${issue.string}"`);
        highPriorityCount++;
      });
      if (highPriorityIssues.length > 5) {
        console.log(`    ... and ${highPriorityIssues.length - 5} more issues`);
      }
    }
  });
  
  console.log(`\n📋 Recommendations:`);
  console.log(`  1. Replace ${highPriorityCount} high-priority user-facing strings with translation keys`);
  console.log(`  2. Add useTranslation hook to components with hardcoded strings`);
  console.log(`  3. Create translation keys in appropriate namespace files`);
  console.log(`  4. Use t('key') instead of hardcoded strings`);
  
  // Generate detailed JSON report
  const reportPath = path.join(__dirname, '../audit-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  
  return results;
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting hardcoded string audit...');
  
  // Get all files to scan
  const allFiles = [];
  CONFIG.scanDirs.forEach(pattern => {
    const files = glob.sync(pattern, { 
      cwd: path.join(__dirname, '..'),
      ignore: CONFIG.excludePatterns,
    });
    allFiles.push(...files.map(f => path.join(__dirname, '..', f)));
  });
  
  console.log(`📁 Scanning ${allFiles.length} files...`);
  
  // Scan each file
  allFiles.forEach(scanFile);
  
  // Generate report
  generateReport();
  
  // Exit with appropriate code
  const highPriorityIssues = results.files.reduce((count, file) => 
    count + file.issues.filter(issue => issue.severity === 'high').length, 0
  );
  
  if (highPriorityIssues > 0) {
    console.log(`\n⚠️  Found ${highPriorityIssues} high-priority issues that need attention.`);
    process.exit(1);
  } else {
    console.log(`\n✅ No high-priority hardcoded strings found!`);
    process.exit(0);
  }
}

// Run the audit
if (require.main === module) {
  main();
}

module.exports = { scanFile, generateReport, CONFIG };
